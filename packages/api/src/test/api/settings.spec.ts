import { suite, test, timeout } from "mocha-typescript";
import { expect } from "chai";
import { Application } from "express";
import { BaseEntity } from "../../skywind/entities/entity";
import getEntityFactory from "../../skywind/services/entityFactory";
import { application } from "../../skywind/server";
import { createComplexStructure, createDefaultMerchantTypes, truncate } from "../entities/helper";
import * as RoleService from "../../skywind/services/role";
import getPlayerService from "../../skywind/services/brandPlayer";
import { Player } from "../../skywind/entities/player";
import { postgres } from "../../skywind/storage/postgres";
import { default as getUserService, initDB, UserService } from "../../skywind/services/user/user";
import { UserInfo } from "../../skywind/entities/user";
import { BrandEntity } from "../../skywind/entities/brand";
import * as EntityService from "../../skywind/services/entity";
import { EntitySettings } from "../../skywind/entities/settings";
import EntitySettingsService from "../../skywind/services/settings";
import { factory } from "factory-girl";
import { FACTORY } from "../factories/common";

const request = require("supertest");
const uuid = require("uuid");

@suite("Settings API", timeout(20000))
class SettingsSpec {
    public static server: Application;
    public static master: BaseEntity;
    public static entity: BaseEntity;
    public static brand: BaseEntity;
    public static brand2: BaseEntity;
    public static userService: UserService;
    public static token: string;
    public static liveToken: string;
    public static player: Player;
    public static user: UserInfo;
    public static merchant: BrandEntity;
    public static userCreateData;

    public static async before() {
        SettingsSpec.server = await application.get();

        await truncate();

        SettingsSpec.master = await createComplexStructure();
        await initDB();
        await createDefaultMerchantTypes();

        SettingsSpec.entity = await getEntityFactory(SettingsSpec.master).createEntity({
            name: "entity",
            description: "test description",
            defaultCurrency: "USD",
            defaultCountry: "US",
            defaultLanguage: "en",
            jurisdictionCode: "COM",
            merchantTypes: ["ipm"]
        });

        SettingsSpec.brand = await getEntityFactory(SettingsSpec.entity).createBrand({
            name: "testBrand",
            description: "testBrand description",
            defaultCurrency: "USD",
            defaultCountry: "US",
            defaultLanguage: "en",
            jurisdictionCode: "COM",
            webSiteUrl: "http://test123f1.com"
        });

        SettingsSpec.brand2 = await getEntityFactory(SettingsSpec.entity).createBrand({
            name: "testBrand22",
            description: "testBrand description",
            defaultCurrency: "USD",
            defaultCountry: "US",
            defaultLanguage: "en",
            jurisdictionCode: "COM",
            webSiteUrl: "http://test123f12.com"
        });

        const merchantInfo = await getEntityFactory(SettingsSpec.entity).createMerchant({
            name: "merch-for-blocked-players" + Math.random().toFixed(8).substr(3),
            description: "Merchant created for testing merchant suspended players",
            defaultCurrency: "USD",
            defaultCountry: "US",
            defaultLanguage: "en",
            jurisdictionCode: "COM",
            type: "ipm",
            code: "merch_" + Math.random().toFixed(8).substr(3),
            params: {
                param1: "p1",
                param2: "p2",
                isPromoInternal: true
            },
            webSiteUrl: "http://test123f13.com"
        });
        SettingsSpec.merchant = await EntityService.findOne({ key: merchantInfo.key }) as BrandEntity;

        const role = await RoleService.createRole({
            title: "AUDITOR",
            permissions: [
                "entity:view",
                "entity",
                "keyentity:player",
                "keyentity:player:create",
                "settings",
                "player:create",
                "player",
                "user",
                "restricted-countries-solution"
            ],
            entityId: SettingsSpec.entity.id,
            isShared: false
        });

        SettingsSpec.userService = getUserService(SettingsSpec.entity);
        const username = "MisterCheburek";
        const password = uuid.v4();
        SettingsSpec.userCreateData = {
            username,
            password,
            email: `${username}@example.com`,
            roles: [role.toInfo()]
        };
        await SettingsSpec.userService.create(SettingsSpec.userCreateData);

        SettingsSpec.token = await request(SettingsSpec.server)
            .post("/v1/login")
            .send({
                secretKey: SettingsSpec.entity.key,
                username: username,
                password: password,
            })
            .then((response) => {
                expect(response.status).to.be.equal(200);
                return response.body.accessToken;
            });
    }

    public async before() {
        SettingsSpec.player = await getPlayerService().create(SettingsSpec.brand,
            {
                code: "PL00001",
                password: "ACAB!Area51",
                firstName: "f_name",
                lastName: "l_name",
                email: "<EMAIL>",
                customData: ["The roof the roof the roof is on fire"],
                country: "US",
                currency: "USD",
                language: "en",
            });

        SettingsSpec.user = await getUserService(SettingsSpec.brand).create({
            username: "cool_name",
            password: "qwertY!112",
            email: "<EMAIL>"
        });

        for (let i = 0; i < 5; i++) {
            await factory.create(FACTORY.GAME_GROUP, {}, { name: `VIP-${i + 1}`, brandId: SettingsSpec.entity.id });
        }
    }

    public async after() {
        await postgres.query("TRUNCATE players CASCADE;");
        await postgres.query("TRUNCATE users CASCADE;");
        await postgres.query("TRUNCATE game_groups CASCADE;");
        await postgres.query("TRUNCATE entity_info CASCADE;");
    }

    @test("Get entity settings")
    public async getEntitySettings() {
        await request(SettingsSpec.server)
            .get("/v1/settings")
            .set("x-access-token", SettingsSpec.token)
            .then((res) => {
                expect(Object.keys(res.body)).deep.equal(["emailTemplates"]);
            });
    }

    @test("Update entity settings when there's no duplicate players")
    public async updatePlayerUniqueChildEntitySettings() {
        await request(SettingsSpec.server)
            .patch("/v1/settings")
            .set("x-access-token", SettingsSpec.token)
            .send({ isPlayerCodeUniqueInSubtree: true })
            .then((res) => {
                expect(res.status).equal(200);
                expect(Object.keys(res.body))
                    .deep
                    .equal(["emailTemplates", "isPlayerCodeUniqueInSubtree"]);
            });
    }

    @test("Fail to patch entity settings when duplicate player exists")
    public async failsUpdatePlayerUniqueChildEntitySettings() {
        await request(SettingsSpec.server)
            .patch("/v1/settings")
            .set("x-access-token", SettingsSpec.token)
            .send({ isPlayerCodeUniqueInSubtree: false });

        const player = await getPlayerService().create(SettingsSpec.brand2,
            {
                code: SettingsSpec.player.code,
                country: "US",
                currency: "USD",
                language: "en",
            });

        await request(SettingsSpec.server)
            .patch("/v1/settings")
            .set("x-access-token", SettingsSpec.token)
            .send({ isPlayerCodeUniqueInSubtree: true })
            .then((res) => {
                expect(res.status).equal(409);
                expect(res.body.code).equal(112);
            });
    }

    @test("Fail to add duplicate player when isPlayerCodeUniqueInSubtree is true")
    public async failCreatePlayerWhenIsPlayerCodeUniqueInSubtreeTrue() {
        await getPlayerService().create(SettingsSpec.brand2, { code: "new-code" });

        await request(SettingsSpec.server)
            .patch("/v1/settings")
            .set("x-access-token", SettingsSpec.token)
            .send({ isPlayerCodeUniqueInSubtree: true });

        const url = `/v1/entities/${SettingsSpec.brand.path.replace(SettingsSpec.entity.path, "")}/players`;
        await request(SettingsSpec.server)
            .post(url)
            .set("x-access-token", SettingsSpec.token)
            .send({ code: "new-code" })
            .then((res) => {
                expect(res.status).equal(409);
                expect(res.body.code).equal(100);
            });
    }

    @test("Try to disable game group inheritance when player of child entity uses parent game group")
    public async disableGameGroupInheritance() {
        await getPlayerService().create(SettingsSpec.brand, { code: "withGameGroup" });
        new EntitySettingsService(SettingsSpec.entity).patch({ gameGroupsInheritance: true });

        await factory.create(FACTORY.GAME_GROUP, { brandId: SettingsSpec.entity.id, name: "parent_game_group" });
        await factory.create(FACTORY.GAME_GROUP, { brandId: SettingsSpec.brand.id, name: "child_game_group" });

        await getPlayerService().update(SettingsSpec.brand, "withGameGroup", { gameGroup: "parent_game_group" });

        await request(SettingsSpec.server)
            .patch("/v1/settings")
            .set("x-access-token", SettingsSpec.token)
            .send({ gameGroupsInheritance: false })
            .then((res) => {
                expect(res.status).equal(400);
                expect(res.body.code).equal(40);
            });

        await getPlayerService().update(SettingsSpec.brand, "withGameGroup", { gameGroup: "child_game_group" });
        await request(SettingsSpec.server)
            .patch("/v1/settings")
            .set("x-access-token", SettingsSpec.token)
            .send({ gameGroupsInheritance: false })
            .then((res) => {
                expect(res.status).equal(200);
                expect(res.body.gameGroupsInheritance).equal(false);
            });
    }

    @test("Fail to set gameGroupsInheritance when parents contain duplicate game groups")
    public async failSetGameGroupInheritanceWhenDuplicateGameGroups() {
        await factory.create(FACTORY.GAME_GROUP, { brandId: SettingsSpec.entity.id, name: "game_group2" });
        await factory.create(FACTORY.GAME_GROUP, { brandId: SettingsSpec.brand.id, name: "game_group2" });

        await request(SettingsSpec.server)
            .patch("/v1/settings")
            .set("x-access-token", SettingsSpec.token)
            .send({ gameGroupsInheritance: true })
            .then((res) => {
                expect(res.status).equal(409);
                expect(res.body.code).equal(112);
            });
    }

    @test("Set gameGroupsInheritance when brands contain duplicate game groups")
    public async setGameGroupInheritanceWhenDuplicateGameGroups() {
        await factory.create(FACTORY.GAME_GROUP, { brandId: SettingsSpec.brand2.id, name: "game_group3" });
        await factory.create(FACTORY.GAME_GROUP, { brandId: SettingsSpec.brand.id, name: "game_group3" });

        await request(SettingsSpec.server)
            .patch("/v1/settings")
            .set("x-access-token", SettingsSpec.token)
            .send({ gameGroupsInheritance: true })
            .then((res) => {
                expect(res.status).equal(200);
                expect(res.body.gameGroupsInheritance).equal(true);
            });
    }

    @test("Update entity settings when there's no duplicate users")
    public async updateUserUniqueChildEntitySettings() {
        await request(SettingsSpec.server)
            .patch("/v1/settings")
            .set("x-access-token", SettingsSpec.token)
            .send({ uniqueUsernamesInSubtree: true })
            .then((res) => {
                expect(res.status).equal(200);
                expect(Object.keys(res.body)).include("uniqueUsernamesInSubtree");
            });
    }

    @test("Fail to patch entity settings when duplicate user exists")
    public async failsUpdateUserUniqueChildEntitySettings() {
        await request(SettingsSpec.server)
            .patch("/v1/settings")
            .set("x-access-token", SettingsSpec.token)
            .send({ uniqueUsernamesInSubtree: false });

        await getUserService(SettingsSpec.brand2).create({
            username: SettingsSpec.user.username,
            email: "<EMAIL>",
            password: "zxc"
        });

        await request(SettingsSpec.server)
            .patch("/v1/settings")
            .set("x-access-token", SettingsSpec.token)
            .send({ uniqueUsernamesInSubtree: true })
            .then((res) => {
                expect(res.status).equal(409);
                expect(res.body.code).equal(112);
            });
    }

    @test("Fail to add duplicate user when uniqueUsernamesInSubtree is true")
    public async failCreateUserWhenUniqueUsernamesInSubtreeTrue() {
        const createUserData = {
            username: "new-username",
            email: "<EMAIL>",
            password: "z!Q121xc"
        };
        await getUserService(SettingsSpec.brand2).create(createUserData);
        await SettingsSpec.userService.create(SettingsSpec.userCreateData);

        await request(SettingsSpec.server)
            .patch("/v1/settings")
            .set("x-access-token", SettingsSpec.token)
            .send({ uniqueUsernamesInSubtree: true });

        const url = `/v1/entities/${SettingsSpec.brand.path.replace(SettingsSpec.entity.path, "")}/users`;
        await request(SettingsSpec.server)
            .post(url)
            .set("x-access-token", SettingsSpec.token)
            .send(createUserData)
            .then((res) => {
                expect(res.status).equal(409);
                expect(res.body.code).equal(200);
            });
    }

    @test("Update entity settings with default game group")
    public async updateDefaultGameGroupEntitySettings() {
        await request(SettingsSpec.server)
            .patch("/v1/settings")
            .set("x-access-token", SettingsSpec.token)
            .send({ defaultGameGroup: "VIP-1", gameGroupsInheritance: true })
            .then((res) => {
                expect(res.status).equal(200);
                expect(Object.keys(res.body).some(key => key === "defaultGameGroup")).eq(true);
            });
    }

    @test("Update entity settings with uboShowOnlyAnalytics")
    public async updateUboShowOnlyAnalyticsEntitySettings() {
        await request(SettingsSpec.server)
            .patch("/v1/settings")
            .set("x-access-token", SettingsSpec.token)
            .send({ uboShowOnlyAnalytics: true })
            .then((res) => {
                expect(res.status).equal(200);
                expect(res.body.uboShowOnlyAnalytics).equal(true);
            });

        // Test setting it to false
        await request(SettingsSpec.server)
            .patch("/v1/settings")
            .set("x-access-token", SettingsSpec.token)
            .send({ uboShowOnlyAnalytics: false })
            .then((res) => {
                expect(res.status).equal(200);
                expect(res.body.uboShowOnlyAnalytics).equal(false);
            });
    }

    @test("Login permissions filtered when uboShowOnlyAnalytics is true")
    public async loginPermissionsFilteredWhenUboShowOnlyAnalyticsIsTrue() {
        // First, set uboShowOnlyAnalytics to true
        await request(SettingsSpec.server)
            .patch("/v1/settings")
            .set("x-access-token", SettingsSpec.token)
            .send({ uboShowOnlyAnalytics: true });

        // Test login with filtered permissions
        await request(SettingsSpec.server)
            .post("/login")
            .send({
                secretKey: SettingsSpec.entity.key,
                username: SettingsSpec.user.username,
                password: "password"
            })
            .then((res) => {
                expect(res.status).equal(200);
                expect(res.body.grantedPermissions).to.exist;
                if (res.body.grantedPermissions && res.body.grantedPermissions.permissions) {
                    expect(res.body.grantedPermissions.permissions).to.not.include("hub:casino");
                    expect(res.body.grantedPermissions.permissions).to.not.include("hub:analytics");
                }
            });

        // Reset setting to false
        await request(SettingsSpec.server)
            .patch("/v1/settings")
            .set("x-access-token", SettingsSpec.token)
            .send({ uboShowOnlyAnalytics: false });
    }

    @test("Fail to update entity settings with default game group - no game group")
    public async failUpdateDefaultGameGroupEntitySettings() {
        await request(SettingsSpec.server)
            .patch("/v1/settings")
            .set("x-access-token", SettingsSpec.token)
            .send({ defaultGameGroup: "VIP-10", gameGroupsInheritance: true })
            .then((res) => {
                expect(res.status).equal(404);
                expect(res.body.code).equal(211);
            });

        await request(SettingsSpec.server)
            .patch("/v1/settings")
            .set("x-access-token", SettingsSpec.token)
            .send({ gameGroupsInheritance: false })
            .then((res) => {
                expect(res.status).equal(200);
                expect(res.body.gameGroupsInheritance).equal(false);
            });

        // disable inheritance - game group not found as it's created on parent level
        await request(SettingsSpec.server)
            .patch(`/v1/entities/${SettingsSpec.brand.path.replace(SettingsSpec.entity.path, "")}/settings`)
            .set("x-access-token", SettingsSpec.token)
            .send({ defaultGameGroup: "VIP-1" })
            .then((res) => {
                expect(res.status).equal(404);
                expect(res.body.code).equal(211);
            });
    }

    @test("Validate maxTestPlayers count on patch")
    public async validateMaxTestPlayers() {

        const maxTestPlayers = 4;
        for (let i = 0; i < maxTestPlayers - 1; i++) {
            await getPlayerService().create(SettingsSpec.merchant, { code: `test_pl_00${i}`, isTest: true });
        }

        const merchantPath = SettingsSpec.merchant.path.replace(SettingsSpec.entity.path, "");
        await request(SettingsSpec.server)
            .patch(`/v1/entities/${merchantPath}/settings`)
            .set("x-access-token", SettingsSpec.token)
            .send({ maxTestPlayers })
            .then((res) => {
                expect(res.status).equal(200);
                expect(res.body.maxTestPlayers).equal(maxTestPlayers);
            });

        await request(SettingsSpec.server)
            .patch(`/v1/entities/${merchantPath}/settings`)
            .set("x-access-token", SettingsSpec.token)
            .send({ maxTestPlayers: maxTestPlayers - 1 })
            .then((res) => {
                expect(res.status).equal(403);
                expect(res.body.code).equal(707);
                expect(res.body.message)
                    .equal(`Brand has exceeded ${maxTestPlayers - 1} of test players. It's max number.`);
            });
    }

    @test("Full rewrite of settings")
    public async putSettings() {
        let expectedSettings: EntitySettings = {
            "emailTemplates": {
                "passwordRecovery": {
                    "from": "Skywind <<EMAIL>>",
                    "subject": "Skywind Password Reset",
                    "html": "\n{{username}},\n<p>\nTo reset your password, please use the following link: <a href=\"http://localhost:6080/?token={{token}}\">click to reset\n</a>.\nIt will expire in 15 minutes — after that you'll need to request a new one.\n</p>\n<p>\nIf you didn't request this change, please let us know by replying to this email.\n</p>\n"
                },
                "changeEmail": {
                    "from": "Skywind <<EMAIL>>",
                    "subject": "Skywind Change Email Confirmation",
                    "html": "\n{{username}},\n<p>\nTo confirm your email, please use the following link: <a href=\"http://localhost:6080/?token={{token}}\">click to confirm\n</a>.\nIt will expire in 15 minutes — after that you'll need to request a new one.\n</p>\n<p>\nIf you didn't request this change, please let us know by replying to this email.\n</p>\n"
                }
            },
            "isPlayerCodeUniqueInSubtree": true,
            "uniqueUsernamesInSubtree": true,
            "defaultGameGroup": "VIP-1",
            "gameGroupsInheritance": false
        };

        await request(SettingsSpec.server)
            .get("/v1/settings")
            .set("x-access-token", SettingsSpec.token)
            .then((res) => {
                expect(res.status).equal(200);
                expectedSettings = {
                    ...expectedSettings,
                    isPlayerCodeUniqueInSubtree: undefined,
                    uniqueUsernamesInSubtree: undefined,
                    dynamicRoutingEnabled: true,
                    newLimitsEnabled: true
                };

                return request(SettingsSpec.server)
                    .put("/v1/settings")
                    .set("x-access-token", SettingsSpec.token)
                    .send(expectedSettings);
            })
            .then((res) => {
                expect(res.body).deep.equal(JSON.parse(JSON.stringify(expectedSettings)));
            });
    }

    @test("CRUD bowhitelist entity settings")
    public async boWhiteList() {
        await this.ipWhiteList("bowhitelist");
    }

    @test("CRUD ip-whitelist/bo entity settings")
    public async boIpWhiteList() {
        await this.ipWhiteList("ip-whitelist/bo");
    }

    @test("CRUD ip-whitelist/user entity settings")
    public async userIpWhiteList() {
        await this.ipWhiteList("ip-whitelist/user");
    }

    private async ipWhiteList(patch: string) {
        const merchantPath = SettingsSpec.merchant.path.replace(SettingsSpec.entity.path, "");
        await request(SettingsSpec.server)
            .get(`/v1/entities/${merchantPath}/settings/${patch}`)
            .set("x-access-token", SettingsSpec.token)
            .then((res) => {
                expect(res.status).equal(404);
            });

        const whitelist1 = ["***********", "***********"];
        await request(SettingsSpec.server)
            .patch(`/v1/entities/${merchantPath}/settings/${patch}`)
            .set("x-access-token", SettingsSpec.token)
            .send(whitelist1)
            .then((res) => {
                expect(res.status).equal(200);
                expect(res.body).deep.equal(whitelist1);
            });

        const whitelist2 = ["***********", "***********"];
        await request(SettingsSpec.server)
            .post(`/v1/entities/${merchantPath}/settings/${patch}`)
            .set("x-access-token", SettingsSpec.token)
            .send(whitelist2.concat(["***********"]))
            .then((res) => {
                expect(res.status).equal(200);
                expect(res.body).deep.equal(whitelist1.concat(whitelist2));
            });

        await request(SettingsSpec.server)
            .delete(`/v1/entities/${merchantPath}/settings/${patch}`)
            .set("x-access-token", SettingsSpec.token)
            .send(whitelist1)
            .then((res) => {
                expect(res.status).equal(200);
                expect(res.body).deep.equal(whitelist2);
            });

        await request(SettingsSpec.server)
            .delete(`/v1/entities/${merchantPath}/settings/${patch}`)
            .set("x-access-token", SettingsSpec.token)
            .send(whitelist2)
            .then((res) => {
                expect(res.status).equal(200);
                expect(res.body).deep.equal([]);
            });
    }

    @test("Validate useCountriesFromJurisdiction on patch /entities/{path}/restricted-countries-solution")
    public async validatePatchRestrictedCountriesSolution() {
        await request(SettingsSpec.server)
            .patch(`/v1/entities/${SettingsSpec.brand.path.replace(SettingsSpec.entity.path,
                "")}/restricted-countries-solution`)
            .set("x-access-token", SettingsSpec.token)
            .send({ useCountriesFromJurisdiction: true })
            .then((res) => {
                expect(res.status).equal(200);
                expect(res.body.useCountriesFromJurisdiction).equal(true);
            });

        await request(SettingsSpec.server)
            .patch(`/v1/entities/${SettingsSpec.brand.path.replace(SettingsSpec.entity.path,
                "")}/restricted-countries-solution`)
            .set("x-access-token", SettingsSpec.token)
            .send({ useCountriesFromJurisdiction: false })
            .then((res) => {
                expect(res.status).equal(200);
                expect(res.body.useCountriesFromJurisdiction).equal(false);
            });

        await request(SettingsSpec.server)
            .patch(`/v1/entities/${SettingsSpec.brand.path.replace(SettingsSpec.entity.path,
                "")}/restricted-countries-solution`)
            .set("x-access-token", SettingsSpec.token)
            .send({})
            .then((res) => {
                expect(res.status).equal(400);
            });

        await request(SettingsSpec.server)
            .patch(`/v1/entities/${SettingsSpec.brand.path.replace(SettingsSpec.entity.path,
                "")}/restricted-countries-solution`)
            .set("x-access-token", SettingsSpec.token)
            .send({ "foo": "bar" })
            .then((res) => {
                expect(res.status).equal(400);
            });
    }

    @test("Validate that patch /entities/{path}/restricted-countries-solution will not rewrite other settings")
    public async validatePatchRestrictedCountriesSolutionWillNotRewriteSettings() {
        const expectedSettings: EntitySettings = {
            "emailTemplates": {
                "passwordRecovery": {
                    "from": "Skywind <<EMAIL>>",
                    "subject": "Skywind Password Reset",
                    "html": "\n{{username}},\n<p>\nTo reset your password, please use the following link: <a href=\"http://localhost:6080/?token={{token}}\">click to reset\n</a>.\nIt will expire in 15 minutes — after that you'll need to request a new one.\n</p>\n<p>\nIf you didn't request this change, please let us know by replying to this email.\n</p>\n"
                },
                "changeEmail": {
                    "from": "Skywind <<EMAIL>>",
                    "subject": "Skywind Change Email Confirmation",
                    "html": "\n{{username}},\n<p>\nTo confirm your email, please use the following link: <a href=\"http://localhost:6080/?token={{token}}\">click to confirm\n</a>.\nIt will expire in 15 minutes — after that you'll need to request a new one.\n</p>\n<p>\nIf you didn't request this change, please let us know by replying to this email.\n</p>\n"
                }
            },
            "defaultGameGroup": "VIP-1",
            "gameGroupsInheritance": false,
            "dynamicRoutingEnabled": true,
            "newLimitsEnabled": true
        };

        await request(SettingsSpec.server)
            .patch(`/v1/entities/${SettingsSpec.brand.path.replace(SettingsSpec.entity.path,
                "")}/settings`)
            .set("x-access-token", SettingsSpec.token)
            .send({ "useCountriesFromJurisdiction": false })
            .then((res) => {
                expect(res.status).equal(200);
                expect(res.body)
                    .deep
                    .equal(JSON.parse(JSON.stringify({ ...expectedSettings, useCountriesFromJurisdiction: false })));
            });

        await request(SettingsSpec.server)
            .patch(`/v1/entities/${SettingsSpec.brand.path.replace(SettingsSpec.entity.path,
                "")}/restricted-countries-solution`)
            .set("x-access-token", SettingsSpec.token)
            .send({ "useCountriesFromJurisdiction": true })
            .then((res) => {
                expect(res.status).equal(200);
                expect(res.body)
                    .deep
                    .equal(JSON.parse(JSON.stringify({ ...expectedSettings, useCountriesFromJurisdiction: true })));
            });
    }
}
