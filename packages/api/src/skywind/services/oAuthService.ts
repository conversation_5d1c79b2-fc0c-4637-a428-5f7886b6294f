import { GrantType, OAuthClient, RefreshTokenResponse, TokenResponse } from "@skywind-group/sw-falcon-oauth";
import config from "../config";
import { lazy, token, logging } from "@skywind-group/sw-utils";
import { BaseEntity } from "../entities/entity";
import EntityCache from "../cache/entity";
import * as Errors from "../errors";
import * as RolesCache from "../cache/role";
import { getPermissions } from "./role";
import { SUPER_ADMIN_ROLE_ID } from "../utils/common";
import {
    blockUser,
    checkUserBlocked,
    checkUserBlockedByLoginAttempts,
    SECURITY_AUTH_TYPE,
} from "./security";
import { IRefreshTokenModel } from "../models/refreshToken";
import { decode } from "jsonwebtoken";
import { UserModel } from "../models/user";
import { UserImpl } from "./user/user";
import { UserAuthServiceImpl } from "./user/userAuth";
import { EntitySettings } from "../entities/settings";
import { getEntitySettings } from "./settings";
import logger from "../utils/logger";
import { OAuthData, OAuthTokenInfo, OAuthTokenResult } from "../entities/oAuth";
import { ContextVariables } from "../utils/contextVariables";
import { decodeId } from "../utils/publicid";
import { Models } from "../models/models";
import { createHash } from "../utils/hash";
import { InvalidAccessToken } from "../errors";
import { sequelize } from "../storage/db";
import { QueryTypes } from "sequelize";

type OAuthTokenMetaData = { sub: any; aud: any };

class OAuthService {
    private readonly oAuthClient: OAuthClient;
    private readonly refreshTokenModel: IRefreshTokenModel;
    private readonly userModel: typeof UserModel;
    private readonly log: logging.Logger;

    constructor() {
        this.oAuthClient = new OAuthClient({
            oAuthBaseUrl: config.oAuth.oAuthBaseUrl,
            clientId: config.oAuth.clientId,
            clientSecret: config.oAuth.clientSecret
        });
        this.refreshTokenModel = Models.RefreshTokenModel;
        this.userModel = Models.UserModel;
        this.log = logger("oauth-service");
    }

    public async getToken({
                              authorizationCode,
                              redirectUri,
                              language,
                              referer,
                              ip,
                              grantType = GrantType.ACCESS_TOKEN
                          }: OAuthData): Promise<OAuthTokenInfo> {
        const response = await this.oAuthClient.getToken(authorizationCode, redirectUri, grantType);
        const decoded = await decode(response.token) as OAuthTokenMetaData;

        const { userId, entityId } = this.decodeSub(decoded.sub);
        const entity = await EntityCache.findById(entityId);
        const userAuthService = new UserAuthServiceImpl(entity);
        let userBlockedByLoginAttempts: boolean = false;
        let user: UserModel;
        try {
            await userAuthService.validateEntity(referer, ip);
            user = await this.findUser(userId);
        } catch (e) {
            this.log.error(e);
            return Promise.reject(new Errors.GenericUserLoginError());
        }
        const masterUser: boolean = entity.isMaster();
        if (!masterUser) {
            userBlockedByLoginAttempts = await checkUserBlockedByLoginAttempts(entity.key, user.get("username"));
        }
        if (!masterUser) {
            checkUserBlocked(user);
        }
        if (userBlockedByLoginAttempts) {
            await blockUser(user);
            return Promise.reject(new Errors.UserAuthenticationBlocked(SECURITY_AUTH_TYPE.USER));
        }

        const settings: EntitySettings = await getEntitySettings(entity.path);

        if (config.twoFA.isEnabled && settings?.twoFactorAuthSettings?.isAuthEnabled) {
            const twoFALoginData = await userAuthService.checkAndGetTwoFALoginData(user, settings, language);
            return twoFALoginData as any;
        }

        await userAuthService.checkPasswordResetRequired(user, entity, settings);

        // Logging Last user success log in
        // With silent: true the updatedAt timestamp will not be updated.
        // Note: Silent work only in case: update(keys: Object, options?: InstanceUpdateOptions)
        // without "await"
        user.update({ lastLogin: new Date() }, { silent: true });
        return this.getTokenInfo(response, entity, new UserImpl(user), settings);
    }

    public async tokenRefresh(expiredAccessToken: string, isRefresh?: boolean): Promise<OAuthTokenInfo> {
        const tokenInfo = await this.parseToken(expiredAccessToken, true, true);
        const accessTokenExpiration = new Date(tokenInfo.exp * 1000);
        const currentTime = new Date();
        const tokenRefreshGracePeriod = config.oAuth.tokenRefreshGracePeriod * 60 * 60 * 1000;

        if (isRefresh && accessTokenExpiration < currentTime) {
            throw new InvalidAccessToken();
        }

        if (currentTime.getTime() - accessTokenExpiration.getTime() > tokenRefreshGracePeriod) {
            throw new InvalidAccessToken();
        }

        const refreshToken = await this.findRefreshToken(expiredAccessToken);
        const response = await this.oAuthClient.tokenRefresh(
            refreshToken
        );
        const decoded = decode(response.token) as OAuthTokenMetaData;
        const { userId, entityId } = this.decodeSub(decoded.sub);
        const entity = await EntityCache.findById(entityId);
        const user = await this.findUser(userId);
        const settings = await getEntitySettings(entity.path);
        return this.getTokenInfo(response, entity, new UserImpl(user), settings);
    }

    private async getTokenInfo(
        response: TokenResponse | RefreshTokenResponse,
        entity: BaseEntity,
        user: UserImpl,
        settings?: EntitySettings
    ): Promise<OAuthTokenInfo> {
        // Filter permissions based on uboShowOnlyAnalytics setting
        let filteredPermissions = user.grantedPermissions;
        if (settings?.uboShowOnlyAnalytics === true) {
            filteredPermissions = user.grantedPermissions.filter(permission =>
                permission !== "hub:casino" && permission !== "hub:analytics"
            );
        }

        return {
            key: entity.key,
            username: user.username,
            accessToken: response.token,
            lastPasswordUpdate: user.passwordChangedAt,
            expiresAt: new Date(response.expiresAt),
            refreshTokenExpiresAt: response.refreshTokenExpiresAt && new Date(response.refreshTokenExpiresAt),
            grantedPermissions: {
                permissions: filteredPermissions
            }
        };
    }

    private async findUser(userId: number): Promise<UserModel> {
        return this.userModel.findByPk(userId, {
            include: [{ model: Models.RoleModel }]
        });
    }

    public async logout(expiredAccessToken: string): Promise<void> {
        const refreshToken = await this.findRefreshToken(expiredAccessToken);
        return this.oAuthClient.logout(refreshToken);
    }

    private async findRefreshToken(expiredAccessToken: string): Promise<string> {
        const decoded = decode(expiredAccessToken) as OAuthTokenMetaData;
        const { userId } = this.decodeSub(decoded.sub);
        const query = `
            SELECT * FROM oauth_refresh_tokens
            WHERE user_id = :userId
            AND EXISTS (
                SELECT 1 FROM oauth_clients
                WHERE oauth_clients.id = oauth_refresh_tokens.client_id
                AND oauth_clients.client_id = :clientId
            );
        `;

        const [refreshTokenEntity] = await sequelize.query(query, {
            replacements: {
                userId,
                clientId: decoded.aud
            },
            type: QueryTypes.SELECT,
            model: this.refreshTokenModel,
            mapToModel: true
        });

        if (!refreshTokenEntity) {
            throw new Errors.InvalidRefreshToken();
        }

        if (refreshTokenEntity.get("expiresAt") < new Date()) {
            throw new Errors.RefreshTokenExpired();
        }

        return refreshTokenEntity.get("token");
    }

    public async parseToken(
        accessToken: string,
        ignoreExpiration?: boolean,
        withTimestamps?: boolean
    ): Promise<OAuthTokenResult> {
        try {
            const decoded = await this.oAuthClient.verifyToken(accessToken, ignoreExpiration);
            const { entityId, userId } = this.decodeSub(decoded.sub);
            const entity: BaseEntity = await EntityCache.findById(entityId);
            if (!entity) {
                return Promise.reject(new Errors.EntityCouldNotBeFound());
            }
            let permissions = decoded.grantedPermissions;
            let isSuperAdmin = decoded.isSuperAdmin;

            if (decoded.roles) {
                decoded.roles = decoded.roles.map((role: string) => decodeId(role));
                const roles = await RolesCache.get(...decoded.roles);
                permissions = getPermissions(roles);

                isSuperAdmin = roles.some(role => role.id === SUPER_ADMIN_ROLE_ID ||
                    role.id === config.security.superAdminRoleId);
            }

            const sessionId = this.createSessionId(decoded.sub, decoded.iat);

            ContextVariables.setEntity(entity);
            ContextVariables.setUserId(userId);
            ContextVariables.setSessionId(sessionId);

            const result: OAuthTokenResult = {
                keyEntity: entity,
                permissions: {
                    grantedPermissions: permissions
                },
                userId,
                sessionId,
                username: decoded.username,
                isSuperAdmin
            };

            if (withTimestamps) {
                result.iat = decoded.iat;
                result.exp = decoded.exp;
            }

            return result;
        } catch (err) {
            if (err instanceof token.TokenVerifyException) {
                return Promise.reject(new Errors.AccessTokenError());
            }
            if (err instanceof token.TokenExpiredException) {
                return Promise.reject(new Errors.AccessTokenExpired());
            }
            return Promise.reject(err);
        }
    }

    private createSessionId(sub: string, iat: number): string {
        const hash = createHash(`${sub}-${iat}`, 32);
        return [
            hash.substring(0, 8),
            hash.substring(8, 12),
            hash.substring(12, 16),
            hash.substring(16, 20),
            hash.substring(20, 32),
        ].join("-");
    }

    private decodeSub(sub: string): { entityId: number; userId: number } {
        const base64 = sub.replace(/-/g, "+").replace(/_/g, "/");
        const decodedPayload = Buffer.from(base64, "base64").toString();
        const { e, u } = JSON.parse(decodedPayload);
        const entityId = decodeId(e);
        const userId = decodeId(u);

        return { entityId, userId };
    }
}

const container = lazy(() => new OAuthService());

export function getOAuthService() {
    return container.get();
}
