import {
    BonusCoinRewardInfo,
    CommonRewardData, Condition,
    ExpiringReward,
    FreebetRewardInfo,
    FreebetSimplePromoInfo, MAP_PROMO_PERMISSIONS, PROMO_OPERATIONS, PROMO_OWNER, PROMO_REWARD_INTERVAL_TYPE,
    PROMO_STATE,
    PROMO_STATUS,
    PromoProjection, PromotionAttributes,
    PromotionInfo
} from "../../entities/promotion";
import * as sequelize from "sequelize";
import { DestroyOptions, IncludeOptions, literal, Op, Transaction, WhereOptions } from "sequelize";
import {
    PromotionDBInstance
} from "../../models/promotion";
import * as Errors from "../../errors";
import { OperationForbidden, OptimisticLockException } from "../../errors";
import {
    createBonusCoinRewards,
    createFreebetRewards,
    deletePromoRewards,
    PromotionBonusCoinRewardImpl,
    PromotionFreebetRewardImpl,
    PromotionRewardImpl,
    updateRewardExpiration,
    validateBonusCoinRewards,
    validate<PERSON><PERSON>betRewards,
} from "./promotionReward";
import { sequelize as db } from "../../storage/db";
import { BaseEntity } from "../../entities/entity";
import { PagingHelper, PagingInfo, WithPagingInfo } from "../../utils/paginghelper";
import * as FilterService from "../../services/filter";
import { BrandEntity } from "../../entities/brand";
import config from "../../config";
import { LabelImpl } from "../label";
import { Models } from "../../models/models";
import { PlayerPromotionService } from "./playerPromotionService";
import { getMerchantCRUDService } from "../merchant";
import { ApplicationLock, ApplicationLockId } from "../../utils/applicationLock";
import { convertDateToTimezone, convertDateToUTC } from "../../utils/datesHelper";
import * as PlayerRewardServices from "./playerRewardServices";
import { PromotionToPlayer, PromotionToPlayerImpl, PromotionToPlayerStatus } from "../../models/promotionPlayer";
import { getChildIds } from "../entity";
import { PROMO_TYPE } from "@skywind-group/sw-management-promo-wallet";
import { PlayerPromotionInfo } from "./playerPromotionDb";
import logger from "../../utils/logger";
import { destructureEGPPromoId, getEGPConfiguration, getEGPPromoGateway, PROMO_LOCATION } from "./egpPromoGateway";
import { getGame } from "../gameprovider";
import { getEntityGames } from "../entityGameService";

const PromotionModel = Models.Promotion;
const PromotionToPlayerModel = Models.PromotionToPlayerModel;
const PromotionToPlayerUpdateModel = Models.PromotionToPlayerUpdateModel;
const PromotionFreebetRewardModel = Models.PromotionFreebetReward;
const PromotionBonusCoinRewardModel = Models.PromotionBonusCoinReward;
const LabelModel = Models.LabelModel;
const LabelGroupModel = Models.LabelGroupModel;
const EntityModel = Models.EntityModel;
const UserModel = Models.UserModel;

const log = logger("promotion");

export interface ValidationResult {
    passed: boolean;
    failedMessage?: string;
    unsupportedGames?: BnsSupportGameValidationResult;
}

export interface BnsSupportGameValidationResult {
    // gameCode: error message
    [gameCode: string]: string;
}

export async function checkPromoAllowed(entity: BaseEntity, validateBrand: boolean,
                                        bnsPromo: boolean): Promise<void> {
    if (validateBrand && !entity.isBrand()) {
        return Promise.reject(new Errors.NotBrand());
    }

    if (entity.isSuspended()) {
        return Promise.reject(new Errors.ParentSuspendedError());
    }

    if ((entity as BrandEntity).isMerchant) {
        const merchant = await getMerchantCRUDService().findOne(entity as BrandEntity);
        if (!bnsPromo && !(merchant.params.isPromoInternal || merchant.params.supportBothInternalExternalPromo)) {
            return Promise.reject(new Errors.MerchantPromoNotEnabledError());
        }
    }
}

export class PromotionImpl {

    private id?: number;
    private title: string;
    private type: string;
    private active: boolean;
    private startRewardOnGameOpen: boolean;
    private archived: boolean;
    private everStarted: boolean;
    private brandId: number;
    private operatorId: number;
    private totalParticipated: number;
    private totalPayout: number;
    private startDate: Date;
    private endDate: Date;
    private timezone: string;
    private createdAt: Date;
    private updatedAt: Date;
    private createdUserId: number;
    private createdUsername: string;
    private updatedUserId: number;
    private updatedUsername: string;
    private description: string;
    private conditions: Condition;
    private customerIds: Array<string> = [];
    private externalId: string;

    private intervalType: string;
    private daysOfWeek: Array<string> = [];
    private daysOfMonth: Array<string> = [];
    private timeOfDay?: string;

    private rewards: PromotionRewardImpl[];
    private labels: LabelImpl[];
    private brand?: BaseEntity;
    private owner: string;
    private players: PromotionToPlayer[];

    private version: number = 0;

    constructor(item?: PromotionDBInstance, includePlayers = false) {
        if (!item) {
            return;
        }

        this.id = item.get("id");
        this.title = item.get("title");
        this.type = item.get("type");
        this.active = item.get("active");
        this.startRewardOnGameOpen = item.get("startRewardOnGameOpen");
        this.archived = item.get("archived");
        this.everStarted = item.get("everStarted");
        this.brandId = item.get("brandId");
        this.operatorId = item.get("operatorId");

        this.timezone = item.get("timezone");
        this.startDate = item.get("startDate");
        this.endDate = item.get("endDate");
        this.createdAt = item.get("createdAt");
        this.updatedAt = item.get("updatedAt");
        this.createdUserId = item.get("createdUserId");
        this.createdUsername = item.get("createdUser") ? item.get("createdUser").get("username") : undefined;
        this.updatedUserId = item.get("updatedUserId");
        this.updatedUsername = item.get("updatedUser") ? item.get("updatedUser").get("username") : undefined;
        this.description = item.get("description");
        this.conditions = item.get("conditions");
        this.customerIds = item.get("customerIds");
        this.owner = item.get("owner");

        this.intervalType = item.get("intervalType");
        this.daysOfWeek = item.get("daysOfWeek");
        this.daysOfMonth = item.get("daysOfMonth");
        this.timeOfDay = item.get("timeOfDay");
        this.version = item.get("version");
        this.externalId = item.get("externalId");

        this.rewards = this.extractRewards(item);

        const labelsDB = item.get("promotionLabels");
        if (labelsDB?.length > 0) {
            this.labels = labelsDB.map(labelDB => new LabelImpl(labelDB));
        } else {
            this.labels = [];
        }

        const playersDB = item.get("players");
        if (playersDB) {
            this.totalParticipated = playersDB.length;
            if (includePlayers) {
                this.players = playersDB.map(p => Object.assign(new PromotionToPlayerImpl(), p.toJSON()));
            }
        }
        this.brand = item.get("brand");
    }

    protected extractRewards(item: PromotionDBInstance): PromotionRewardImpl[] {
        const freebetRewards = item.get("freebetRewards");
        if (freebetRewards?.length > 0) {
            return freebetRewards.map(
                (rewardDB) => new PromotionFreebetRewardImpl(rewardDB)
            );
        }

        const bonusCoinRewards = item.get("bonusCoinRewards");
        if (bonusCoinRewards?.length > 0) {
            return bonusCoinRewards.map(
                (rewardDB) => new PromotionBonusCoinRewardImpl(rewardDB)
            );
        }

        return [];
    }

    public toInfo(keyEntity?: BaseEntity): PromotionInfo {
        const brandPath = this.brand && keyEntity
                          ? this.brand.path.replace(keyEntity.path, "")
                          : undefined;
        return {
            id: this.id,
            title: this.title,
            type: this.type,
            status: this.active ? PROMO_STATUS.ACTIVE : PROMO_STATUS.INACTIVE,
            state: this.getState(),
            startRewardOnGameOpen: this.startRewardOnGameOpen,
            conditions: this.conditions,
            archived: this.archived,
            everStarted: this.everStarted,
            brandId: this.brandId,
            totalParticipated: this.totalParticipated,
            totalPayout: this.totalPayout,
            startDate: convertDateToTimezone(this.startDate, this.timezone).toJSON(),
            endDate: convertDateToTimezone(this.endDate, this.timezone).toJSON(),
            owner: this.owner,
            timezone: this.timezone,
            createdUserId: this.createdUserId,
            createdUsername: this.createdUsername,
            updatedUserId: this.updatedUserId,
            updatedUsername: this.updatedUsername,
            description: this.description,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,

            intervalType: this.intervalType,
            daysOfWeek: this.daysOfWeek,
            daysOfMonth: this.daysOfMonth,
            timeOfDay: this.timeOfDay,

            rewards: this.rewards && this.rewards.map(reward => reward.toInfo()),
            labels: this.labels && this.labels.map(label => label.toInfo()),
            customerIds: this.customerIds,
            brandPath: brandPath,
            externalId: this.externalId
        };
    }

    public toShortInfo(keyEntity?: BaseEntity): PromotionInfo {
        const brandPath = this.brand && keyEntity
                          ? this.brand.path.replace(keyEntity.path, "")
                          : undefined;

        const result: PromotionInfo = {
            id: this.id,
            title: this.title,
            type: this.type,
            status: this.active ? PROMO_STATUS.ACTIVE : PROMO_STATUS.INACTIVE,
            state: this.getState(),
            startRewardOnGameOpen: this.startRewardOnGameOpen,
            brandId: this.brandId,
            startDate: convertDateToTimezone(this.startDate, this.timezone).toJSON(),
            endDate: convertDateToTimezone(this.endDate, this.timezone).toJSON(),
            owner: this.owner,
            timezone: this.timezone,
            description: this.description,
            createdAt: this.createdAt,
            createdUserId: this.createdUserId,
            createdUsername: this.createdUsername,
            everStarted: this.everStarted,
            labels: this.labels && this.labels.map(label => label.toInfo()),
            intervalType: this.intervalType,
            daysOfWeek: this.daysOfWeek,
            daysOfMonth: this.daysOfMonth,
            timeOfDay: this.timeOfDay,
            totalParticipated: this.totalParticipated,
            brandPath: brandPath,
            externalId: this.externalId,
            rewards: this.rewards && this.rewards.map(reward => reward.toInfo()),
        };
        if (!!this.operatorId) {
            result["operatorId"] = this.operatorId;
        }
        if (this.players) {
            result.players = this.players;
        }
        this.assignGamesList(result);
        return result;
    }

    private assignGamesList(promotionInfo: PromotionInfo) {
        if (!promotionInfo.rewards || promotionInfo.rewards.length === 0) {
            return;
        }

        if (promotionInfo.type === PROMO_TYPE.BONUS_COIN) {
            promotionInfo.games = (promotionInfo.rewards[0] as BonusCoinRewardInfo).games;
        }

        if (promotionInfo.type === PROMO_TYPE.FREEBET) {
            promotionInfo.games = (promotionInfo.rewards[0] as FreebetRewardInfo)
                .games.map(x => x.gameCode);
        }
    }

    public toDBAttributes(): PromotionAttributes {
        return {
            id: this.id,
            title: this.title,
            type: this.type,
            active: this.active,
            archived: this.archived,
            everStarted: this.everStarted,
            startRewardOnGameOpen: this.startRewardOnGameOpen,
            brandId: this.brandId,
            timezone: this.timezone,
            startDate: this.startDate,
            endDate: this.endDate,
            createdUserId: this.createdUserId,
            updatedUserId: this.updatedUserId,
            description: this.description,
            conditions: this.conditions,
            customerIds: this.customerIds,
            owner: this.owner,

            intervalType: this.intervalType,
            daysOfWeek: this.daysOfWeek,
            daysOfMonth: this.daysOfMonth,
            timeOfDay: this.timeOfDay,
            version: this.version,
            externalId: this.externalId
        };
    }

    public getState(): string {
        const currentDate = new Date();
        if (this.startDate > currentDate) {
            return PROMO_STATE.PENDING;
        } else if (this.startDate <= currentDate && this.endDate >= currentDate) {
            return this.isActive() || this.everStarted ? PROMO_STATE.IN_PROGRESS : PROMO_STATE.PENDING;
        } else if (this.everStarted) {
            return PROMO_STATE.FINISHED;
        }
        return PROMO_STATE.EXPIRED;
    }

    public setId(value: number): PromotionImpl {
        this.id = value;
        return this;
    }

    public getId(): number {
        return this.id;
    }

    public setTitle(value: string): PromotionImpl {
        this.title = value;
        return this;
    }

    public getTitle(): string {
        return this.title;
    }

    public setConditions(value: Condition): PromotionImpl {
        this.conditions = value;
        return this;
    }

    public getConditions(): Condition {
        return this.conditions;
    }

    public setType(value: string): PromotionImpl {
        this.type = value;
        return this;
    }

    public getType(): string {
        return this.type;
    }

    public getStatus(): string {
        return this.isActive() ? PROMO_STATUS.ACTIVE : PROMO_STATUS.INACTIVE;
    }

    public setStatus(value: string): PromotionImpl {
        this.active = (value === PROMO_STATUS.ACTIVE);
        return this;
    }

    public isActive(): boolean {
        return this.active;
    }

    public setStartRewardOnGameOpen(value: boolean): PromotionImpl {
        this.startRewardOnGameOpen = value;
        return this;
    }

    public isStartRewardOnGameOpen(): boolean {
        return this.startRewardOnGameOpen;
    }

    public setArchived(value: boolean): PromotionImpl {
        this.archived = value;
        return this;
    }

    public isArchived(): boolean {
        return this.archived;
    }

    public setEverStarted(value: boolean): PromotionImpl {
        this.everStarted = value;
        return this;
    }

    public setBrandId(value: number): PromotionImpl {
        this.brandId = value;
        return this;
    }

    public getBrandId(): number {
        return this.brandId;
    }

    public setOperatorId(value: number): PromotionImpl {
        this.operatorId = value;
        return this;
    }

    public setTotalParticipated(value: number): PromotionImpl {
        this.totalParticipated = value;
        return this;
    }

    public setTotalPayout(value: number): PromotionImpl {
        this.totalPayout = value;
        return this;
    }

    public setStartDate(value: Date, timezone?: string): PromotionImpl {
        this.startDate = convertDateToUTC(value || this.startDate || null, timezone);
        return this;
    }

    public getStartDate(): Date {
        return this.startDate;
    }

    public getCreatedAt(): Date {
        return this.createdAt;
    }

    public setEndDate(value: Date, timezone?: string): PromotionImpl {
        this.endDate = convertDateToUTC(value || this.endDate || null, timezone);
        return this;
    }

    public getEndDate(): Date {
        return this.endDate;
    }

    public setOwner(value: string): PromotionImpl {
        this.owner = value || PROMO_OWNER.OPERATOR;
        return this;
    }

    public getOwner(): string {
        return this.owner;
    }

    public setCreatedUser(value: number): PromotionImpl {
        this.createdUserId = value;
        return this;
    }

    public setUpdatedUser(value: number): PromotionImpl {
        this.updatedUserId = value;
        return this;
    }

    public setDescription(value: string): PromotionImpl {
        this.description = value;
        return this;
    }

    public setIntervalType(value: string): PromotionImpl {
        this.intervalType = value;
        return this;
    }

    public setDaysOfWeek(value: Array<string>): PromotionImpl {
        this.daysOfWeek = value;
        return this;
    }

    public setDaysOfMonth(value: Array<string>): PromotionImpl {
        this.daysOfMonth = value;
        return this;
    }

    public setCustomerIds(value: Array<string>): PromotionImpl {
        this.customerIds = value;
        return this;
    }

    public getCustomerIds(): Array<string> {
        return this.customerIds;
    }

    public setTimeOfDay(value: string): PromotionImpl {
        this.timeOfDay = value;
        return this;
    }

    public setRewards(value: PromotionRewardImpl[]): PromotionImpl {
        this.rewards = value;
        return this;
    }

    public setExternalId(value: string): PromotionImpl {
        this.externalId = value;
        return this;
    }

    public isExternal(): boolean {
        return !!this.externalId;
    }

    public getExternalId(): string {
        return this.externalId;
    }

    public getRewards(): PromotionRewardImpl[] {
        return this.rewards;
    }

    public setLabels(value: LabelImpl[]): PromotionImpl {
        this.labels = value;
        return this;
    }

    public getLabels(): LabelImpl[] {
        return this.labels;
    }

    public isRunning(): boolean {
        return this.getState() === PROMO_STATE.IN_PROGRESS && this.isActive();
    }

    public hasFinished(): boolean {
        const state = this.getState();
        return state === PROMO_STATE.FINISHED || state === PROMO_STATE.EXPIRED || this.isArchived();
    }

    public isEverStarted(): boolean {
        return this.everStarted;
    }

    public setTimezone(value: string): PromotionImpl {
        this.timezone = value;
        return this;
    }

    public getTimezone(): string {
        return this.timezone;
    }

    public setVersion(version: number): PromotionImpl {
        this.version = version;
        return this;
    }

    public getVersion(): number {
        return this.version;
    }

    public getRewardById(rewardId): PromotionRewardImpl {
        return this.rewards.find(reward => reward.getId() === rewardId);
    }
}

export function getRewardExpirationDate(startDate: Date, reward: ExpiringReward): number {
    if (reward.expirationDate) {
        return new Date(reward.expirationDate).getTime();
    }
    const expireMs = getRewardExpirationPeriod(reward);
    return expireMs ? startDate.getTime() + expireMs : undefined;
}

export function getRewardExpirationPeriod(reward: ExpiringReward): number {
    switch (reward.expirationPeriodType) {
        case PROMO_REWARD_INTERVAL_TYPE.MINUTELY:
            return reward.expirationPeriod * 60 * 1000;
        case PROMO_REWARD_INTERVAL_TYPE.HOURLY:
            return reward.expirationPeriod * 60 * 60 * 1000;
        case PROMO_REWARD_INTERVAL_TYPE.DAILY:
            return reward.expirationPeriod * 24 * 60 * 60 * 1000;
        case PROMO_REWARD_INTERVAL_TYPE.MONTHLY:
            return reward.expirationPeriod * 30 * 24 * 60 * 60 * 1000;
        case PROMO_REWARD_INTERVAL_TYPE.WEEKLY:
            return reward.expirationPeriod * 7 * 24 * 60 * 60 * 1000;
        default:
    }
}

export async function getPromos(entity: BaseEntity,
                                query: WhereOptions<any>,
                                includePromoTotals: boolean = false,
                                includePlayers = false,
                                includeGames = false): Promise<WithPagingInfo<PromotionImpl>> {
    await checkPromoAllowed(entity, false, true);

    const childIds = getChildIds(entity);
    query["brandId"] = { [Op.in]: [entity.id, ...childIds] };
    if (query["state"]) {
        statesToQuery(FilterService.valueFromQuery(query, "state"), query);
    }
    if (query["status"]) {
        const status = query["status"][Op.eq];
        statusToQuery(status, query);
    }
    if (query["labelsId"]) {
        const dbItems =
            await Models.PromotionLabelModel.findAll({ where: { labelId: { [Op.in]: query["labelsId"][Op.in] } } });
        if (dbItems.length) {
            query["id"] = { [Op.in]: dbItems.map(dbItem => dbItem.promoId) };
        }
        delete query["labelsId"];
    }

    const sortBy = FilterService.getSortKey(query, sortableKeys, "createdAt");
    const sortOrder = FilterService.valueFromQuery(query, "sortOrder") || "DESC";

    const include: IncludeOptions[] = [
        {
            model: LabelModel, as: "promotionLabels"
        },
        {
            model: EntityModel, as: "brand"
        },
        {
            model: UserModel, as: "createdUser"
        },
        {
            model: UserModel, as: "updatedUser"
        }
    ];

    if (includePlayers) {
        include.push({
            model: PromotionToPlayerModel, as: "players"
        });
    }

    if (includeGames) {
        include.push({
            model: PromotionFreebetRewardModel, as: "freebetRewards"
        });
        include.push({
            model: PromotionBonusCoinRewardModel, as: "bonusCoinRewards",
        });
    }

    const limit = FilterService.valueFromQuery(query, "limit");
    const externalId = FilterService.valueFromQuery(query, "externalId", false);
    const title = FilterService.valueFromQuery(query, "title", false);

    const promotions = await PagingHelper.findAndCountAll(PromotionModel, {
        where: query,
        offset: FilterService.valueFromQuery(query, "offset"),
        limit,
        order: literal(`"${sortBy}" ${sortOrder}`),
        include
    }, (item: PromotionDBInstance) => new PromotionImpl(item, includePlayers));

    if (includePromoTotals) {
        const playerCounts = await Models.PromotionToPlayerModel.findAll({
            attributes: ["promotionId", [sequelize.fn("count", sequelize.col("player_code")), "count"]],
            group: ["promotionId"],
            where: {
                promotionId: { [Op.in]: promotions.map((promo) => promo.getId()) },
                status: { [Op.ne]: PromotionToPlayerStatus.REVOKED }
            }
        });
        for (const promo of promotions) {
            const promotion = promo as PromotionImpl;
            const count = playerCounts.find((playerCount) =>
                (playerCount.get("promotionId") === promotion.getId()));
            promotion.setTotalParticipated(count ? +count.get("count") : 0);
        }
    }
    return mergeWithEGPPromotions(entity, promotions, limit, externalId, title);
}

async function mergeWithEGPPromotions(
    entity: BaseEntity,
    internalPromotions: PromotionImpl[] & PagingInfo[],
    limit: number,
    externalId?: string,
    title?: string
): Promise<WithPagingInfo<PromotionImpl>> {
    const pagingInfo = internalPromotions["PAGING_INFO"];
    const difference = (limit || 1000) - internalPromotions.length;
    if (difference <= 0) {
        return internalPromotions;
    }
    const egpPromotions = await getEGPPromotions(
        entity,
        difference,
        externalId,
        title
    );
    const mappedEGPPromotions = egpPromotions.map(egpPromo => {
        return {
            toShortInfo: () => egpPromo
        };
    });
    const mergedPromotions = [...internalPromotions, ...mappedEGPPromotions] as WithPagingInfo<PromotionImpl>;
    pagingInfo.total = mergedPromotions.length;
    mergedPromotions["PAGING_INFO"] = pagingInfo;
    return mergedPromotions;
}

async function getEGPPromotions(
    entity: BaseEntity,
    limit?: number,
    externalId?: string,
    title?: string
): Promise<PromotionInfo[]> {
    const egpCodes = await getEGPCodes(entity);
    if (!egpCodes || !egpCodes.length) {
        return [];
    }
    // We need a more robust logic to combine SW promotions with EGP promotions when filters are present
    const egpPromotions: PromotionInfo[] = [];
    let remainingLimit = limit;
    for (const egpCode of egpCodes) {
        const egpConfiguration = getEGPConfiguration(egpCode, true);
        if (egpConfiguration?.promoLocation === PROMO_LOCATION.EGP) {
            const egpPromoGateway = getEGPPromoGateway(egpConfiguration.url, egpCode);
            const foundEgpPromotions = await egpPromoGateway.getEntityPromotions(
                entity,
                { operatorPromoId: externalId }
            );
            if (remainingLimit === undefined) {
                egpPromotions.push(...foundEgpPromotions);
            } else if (foundEgpPromotions.length < remainingLimit) {
                egpPromotions.push(...foundEgpPromotions);
                remainingLimit -= egpPromotions.length;
            } else {
                egpPromotions.push(...foundEgpPromotions.slice(0, remainingLimit));
            }
        }
    }
    if (title) {
        return egpPromotions.filter(egpPromotion => egpPromotion.title === title);
    }
    return egpPromotions;
}

export async function getEGPCodes(entity: BaseEntity): Promise<string[]> {
    const entityGames = await getEntityGames(entity);
    return [
        ...new Set(entityGames
            .map(entityGame => entityGame.game.gameProvider.code)
            .filter(gameProviderCode => gameProviderCode !== "SW"))
    ];
}

function statusToQuery(status: string, query: WhereOptions<any>) {
    if (status === PROMO_STATUS.ACTIVE) {
        query["active"] = { [Op.eq]: true };
    } else {
        query["active"] = { [Op.eq]: false };
    }
    delete query["status"];
}

function statesToQuery(states: string | string[], query: WhereOptions<any>) {
    if (!Array.isArray(states) || states.length === 1) {
        return stateToQuery(Array.isArray(states) ? states[0] : states, query);
    }
    const statesQuery = [];
    for (const state of states) {
        const stateQuery = {};
        stateToQuery(state, stateQuery);
        statesQuery.push(stateQuery);
    }
    query[Op.or as any] = statesQuery;
    delete query["state"];
}

function stateToQuery(state: string, query: WhereOptions<any>) {
    const currentDate = new Date();
    switch (state) {
        case PROMO_STATE.PENDING:
            query["startDate"] = { [Op.gte]: currentDate };
            break;
        case PROMO_STATE.IN_PROGRESS:
            query["startDate"] = { [Op.lte]: currentDate };
            query["endDate"] = { [Op.gte]: currentDate };
            break;
        case PROMO_STATE.FINISHED:
            query["endDate"] = { [Op.lte]: currentDate };
            query["everStarted"] = true;
            break;
        case PROMO_STATE.EXPIRED:
            query["endDate"] = { [Op.lte]: currentDate };
            query["everStarted"] = false;
            break;
        default:
            break;
    }
    delete query["state"];
}

export async function getPromo(id: number, entity: BaseEntity,
                               includePromoTotals: boolean = false): Promise<PromotionImpl> {
    const destructuredEGPPromoId = destructureEGPPromoId(id);
    if (destructuredEGPPromoId) {
        const { egpPromoId, gameProviderCode } = destructuredEGPPromoId;
        const { url } = getEGPConfiguration(gameProviderCode);
        const promoInfo = await getEGPPromoGateway(url, gameProviderCode).getPromo(egpPromoId);
        return {
            toInfo: () => promoInfo
        } as any;
    }
    const promo = await findOne({ brandId: entity.id, id });
    await checkPromoAllowed(entity, false, promo.getType() === PROMO_TYPE.BONUS_COIN);
    await PlayerRewardServices.get(entity as BrandEntity, promo.getType()).verifyConsistency(promo);

    if (includePromoTotals) {
        const totalParticipated = await PlayerPromotionService.countPromoParticipatedPlayers(id);
        promo.setTotalParticipated(totalParticipated);
    }
    return promo;
}

export function getPromoProjection(entity: BaseEntity,
                                   promoId: number,
                                   currency: string): PromoProjection {
    const mockedData = {
        projected: {
            reach: 1500,
            participants: 2500,
            payout: 10000
        },
        actual: {
            reach: 1700,
            participants: 3000,
            payout: 12000
        }
    };

    return mockedData;
}

// find and return all promotion labels under given brand
export async function getPromoBrandLabels(entity: BaseEntity): Promise<LabelImpl[]> {
    const dbItems = await Models.LabelModel.findAll({
        include: [
            {
                model: Models.Promotion,
                as: "labelPromotions",
                where: { brandId: entity.id },
                attributes: ["brandId"]
            }
        ]
    });
    return dbItems.map(dbItem => new LabelImpl(dbItem));
}

export async function findOne(query: WhereOptions<any>): Promise<PromotionImpl> {
    const promoDBInstance: PromotionDBInstance = await PromotionModel.findOne({
        where: query,
        include: [
            {
                model: PromotionFreebetRewardModel, as: "freebetRewards"
            },
            {
                model: PromotionBonusCoinRewardModel, as: "bonusCoinRewards",
            },
            {
                model: LabelModel, as: "promotionLabels",
                include: [
                    {
                        model: LabelGroupModel,
                        as: "group"
                    }
                ]
            },
            {
                model: UserModel, as: "createdUser"
            },
            {
                model: UserModel, as: "updatedUser"
            }
        ]
    });
    if (!promoDBInstance) {
        return Promise.reject(new Errors.PromotionNotFoundError());
    }
    return new PromotionImpl(promoDBInstance);
}

export async function createPromo(data: PromotionInfo, entity: BaseEntity,
                                  userId: number, validateBrand = true,
                                  upsert = false): Promise<PromotionImpl> {
    await checkPromoAllowed(entity, validateBrand, data.type === PROMO_TYPE.BONUS_COIN);
    const shouldStartImmediately = !data.startDate && (data.status === PROMO_STATUS.ACTIVE);

    const validationResult: ValidationResult = await validatePromo(
        data,
        entity as BrandEntity,
        true,
        shouldStartImmediately
    );
    if (!validationResult.passed) {
        return Promise.reject(new Errors.ValidationError(validationResult.failedMessage));
    }
    removeUnsupportedGamesFromRewards(data, validationResult);

    if (data.type === PROMO_TYPE.FREEBET) {
        const uniqueCodes = await getUniqueGameProviderCodesFromFreeBetRewards(data.rewards);

        if (uniqueCodes.length > 1) {
            throw new OperationForbidden(`Can't add the same promo for games from different game providers: ${uniqueCodes.join()}`);
        }

        const [gameProviderCode] = uniqueCodes;

        if (gameProviderCode !== "SW") {
            const egpConfiguration = getEGPConfiguration(gameProviderCode);
            if (egpConfiguration.promoLocation !== PROMO_LOCATION.SW) {
                data.startDate = convertDateToUTC(new Date(data.startDate), data.timezone).toISOString();
                data.endDate = convertDateToUTC(new Date(data.endDate), data.timezone).toISOString();
                data.brandId = entity.id;
                const egpPromoInfo = await getEGPPromoGateway(egpConfiguration.url, gameProviderCode)
                    .createPromo(data, entity);
                return {
                    toInfo: () => egpPromoInfo
                } as any;
            }
        }
    }

    let result: PromotionImpl;
    await db.transaction(async (transaction: Transaction): Promise<any> => {
        const promo: PromotionImpl = new PromotionImpl()
            .setTitle(data.title)
            .setType(data.type)
            .setStatus(data.status)
            .setStartRewardOnGameOpen(data.startRewardOnGameOpen)
            .setBrandId(entity.id)
            .setConditions(data.conditions)
            .setEverStarted(shouldStartImmediately)
            .setTimezone(data.timezone)
            .setEndDate(new Date(data.endDate), data.timezone)
            .setCreatedUser(userId)
            .setDescription(data.description)
            .setCustomerIds(data.customerIds)
            .setOwner(data.owner)
            .setIntervalType(data.intervalType)
            .setDaysOfWeek(data.daysOfWeek)
            .setDaysOfMonth(data.daysOfMonth)
            .setTimeOfDay(data.timeOfDay)
            .setExternalId(data.externalId);

        if (shouldStartImmediately) {
            promo.setStartDate(new Date());
        } else {
            promo.setStartDate(new Date(data.startDate), data.timezone);
        }

        if (upsert && data.id) {
            await PromotionToPlayerModel.destroy({ where: { promotionId: data.id }, transaction });
            await PromotionToPlayerUpdateModel.destroy({ where: { promotionId: data.id }, transaction });
            await PromotionModel.destroy({
                where: { externalId: data.externalId, brandId: entity.id },
                force: true, cascade: true, transaction
            });
        }

        const item: PromotionDBInstance = await PromotionModel.create(promo.toDBAttributes(), { transaction });

        let rewards = [];

        switch (data.type) {
            case PROMO_TYPE.FREEBET:
                rewards = await createFreebetRewards(data.rewards as FreebetRewardInfo[],
                    item.get("id"),
                    transaction);
                break;
            case PROMO_TYPE.BONUS_COIN:
                rewards = await createBonusCoinRewards(
                    data.rewards as BonusCoinRewardInfo[],
                    item.get("id"),
                    entity,
                    transaction);
                break;
            default:
                await transaction.rollback();
                return Promise.reject(new Errors.ValidationError("Unknown promo type"));
        }

        result = new PromotionImpl(item);
        result.setRewards(rewards);
        result.setLabels([]);

        if (data.customerIds?.length) {
            try {
                const rewardService = PlayerRewardServices.get(entity as BrandEntity, result.getType());
                await rewardService.addPlayers(result, data.customerIds, undefined, transaction, entity);
            } catch (err) {
                await transaction.rollback();
                return Promise.reject(err);
            }
        }

        if (data.players && data.players.length > 0) {
            try {
                const rewardService = PlayerRewardServices.get(entity as BrandEntity, result.getType());
                const playerCodes = data.players.map((p) => p.playerCode);
                await rewardService.addPlayers(result, playerCodes, undefined, transaction, entity);
            } catch (err) {
                await transaction.rollback();
                return Promise.reject(err);
            }
        }
    });
    return result;
}

async function getUniqueGameProviderCodesFromFreeBetRewards(rewards: CommonRewardData[]): Promise<string[]> {
    const freeBetRewards = rewards as FreebetRewardInfo[];
    const gameCodes = freeBetRewards.reduce<string[]>((acc, curr) => {
        acc.push(...curr.games.map(game => game.gameCode));
        return acc;
    }, []);

    const gameProviderCodes = await Promise.all(gameCodes.map(async (gameCode: string) => {
        const game = await getGame(gameCode);
        return game.gameProvider.code;
    }));

    return [...new Set(gameProviderCodes)];
}

export async function savePromo(promo: PromotionImpl, transaction?: Transaction): Promise<PromotionImpl> {
    const oldVersion = promo.getVersion();
    promo.setVersion(oldVersion + 1);

    try {
        const [isUpdated, [updatedPromo]] = await PromotionModel.update(promo.toDBAttributes(), {
            where: {
                id: promo.getId(),
                version: oldVersion,
            },
            returning: true,
            transaction
        });
        if (!isUpdated) {
            return Promise.reject(new Errors.OptimisticLockException());
        }

        return new PromotionImpl(updatedPromo);
    } catch (err) {
        promo.setVersion(oldVersion);
        return Promise.reject(err);
    }
}

export async function updatePromo(data: PromotionInfo, entity: BaseEntity, userId: number): Promise<PromotionImpl> {
    const promo: PromotionImpl = await findOne({ id: data.id, brandId: entity.id });
    await checkPromoAllowed(entity, true, promo.getType() === PROMO_TYPE.BONUS_COIN);

    checkPromoCanBeUpdated(promo);

    await PlayerRewardServices.get(entity as BrandEntity, promo.getType()).verifyConsistency(promo);

    const validationResult: ValidationResult = await validateUpdatePromo(promo, data, entity as BrandEntity);
    if (!validationResult.passed) {
        return Promise.reject(new Errors.ValidationError(validationResult.failedMessage));
    }

    removeUnsupportedGamesFromRewards(data, validationResult);

    await db.transaction(async (transaction: Transaction): Promise<any> => {
        if (promo.isRunning()) {
            // When promo is in progress we can update only end date
            if (data.endDate) {
                promo.setEndDate(new Date(data.endDate), promo.getTimezone());
            }
            promo.setUpdatedUser(userId);
            if (data.rewards) {
                await updateRewardsExpiration(entity as BrandEntity, promo, data, transaction);
            }
        } else {
            data.timezone = data.timezone || promo.getTimezone();

            promo
                .setTitle(data.title)
                .setConditions(data.conditions)
                .setStartRewardOnGameOpen(data.startRewardOnGameOpen)
                .setUpdatedUser(userId)
                .setDescription(data.description)
                .setCustomerIds(data.customerIds)

                .setIntervalType(data.intervalType)
                .setDaysOfWeek(data.daysOfWeek)
                .setDaysOfMonth(data.daysOfMonth)
                .setTimeOfDay(data.timeOfDay)

                .setTimezone(data.timezone)
                .setExternalId(data.externalId);

            if (data.startDate) {
                promo.setStartDate(new Date(data.startDate), data.timezone);
            }

            if (data.endDate) {
                promo.setEndDate(new Date(data.endDate), data.timezone);
            }

            if (data.rewards) {
                promo.setRewards(await setPromoRewards(data, promo.getType(), entity, transaction));
            }

            if (data.customerIds?.length) {
                try {
                    const rewardService = PlayerRewardServices.get(entity as BrandEntity, promo.getType());
                    await rewardService.addPlayers(promo, data.customerIds, undefined, transaction, entity);
                } catch (err) {
                    await transaction.rollback();
                    return Promise.reject(err);
                }
            }
        }

        return savePromo(promo, transaction);
    });
    return promo;
}

async function updateRewardsExpiration(entity: BrandEntity, promo: PromotionImpl, data: PromotionInfo,
                                       transaction: sequelize.Transaction): Promise<void> {
    if (promo.getType() !== PROMO_TYPE.BONUS_COIN) {
        return;
    }

    const rewardWithMaxExpiration = findRewardWithMaxExpirationInUpdate(promo, data);
    for (let i = 0; i < promo.getRewards().length; i++) {
        const updatedReward = data.rewards[i] as ExpiringReward;
        const existingReward = promo.getRewards()[i] as ExpiringReward & PromotionRewardImpl;
        const existingRewardInfo = existingReward.toInfo() as ExpiringReward;
        const prolonged = updatedReward.expirationPeriod !== existingRewardInfo.expirationPeriod
            || updatedReward.expirationPeriodType !== existingRewardInfo.expirationPeriodType;
        if (prolonged) {
            const expReward = existingReward;
            existingReward.expirationPeriod = updatedReward.expirationPeriod;
            existingReward.expirationPeriodType = updatedReward.expirationPeriodType;
            await updateRewardExpiration(promo.getType() as PROMO_TYPE, expReward, transaction);
        }
    }
    if (rewardWithMaxExpiration) {
        const players: PlayerPromotionInfo[] = await PlayerPromotionService.getPromotionPlayers(entity, promo.getId(),
            { status: PromotionToPlayerStatus.STARTED, expireAt: { [Op.gt]: new Date() } });
        if (players.length) {
            const playerCodes = players.map(p => p.playerCode);
            await PlayerPromotionService.updatePromotionPlayers(entity,
                playerCodes,
                promo,
                {
                    expirationPeriod: rewardWithMaxExpiration.expirationPeriod,
                    expirationPeriodType: rewardWithMaxExpiration.expirationPeriodType
                });
        }
    }
}

function findRewardWithMaxExpirationInUpdate(promo: PromotionImpl, data: PromotionInfo): ExpiringReward {
    let maxExpirationIndex = -1;
    let existingRewardsMaxExpiration = 0;
    let updatedRewardsMaxExpiration = 0;

    for (let i = 0; i < promo.getRewards().length; i++) {
        const updatedReward = data.rewards[i] as ExpiringReward;
        const updatedExpiration = getRewardExpirationPeriod(updatedReward);
        if (updatedExpiration > updatedRewardsMaxExpiration) {
            maxExpirationIndex = i;
            updatedRewardsMaxExpiration = updatedExpiration;
        }
        const existingReward = promo.getRewards()[i] as ExpiringReward;
        const existingExpiration = getRewardExpirationPeriod(existingReward);
        if (existingExpiration > existingRewardsMaxExpiration) {
            existingRewardsMaxExpiration = existingExpiration;
        }
    }
    if (updatedRewardsMaxExpiration > existingRewardsMaxExpiration) {
        return data.rewards[maxExpirationIndex] as ExpiringReward;
    }
}

function checkPromoCanBeUpdated(promo: PromotionImpl) {
    if (!promo.getId()) {
        throw new Errors.PromotionNotFoundError();
    }

    if (promo.getState() === PROMO_STATE.FINISHED || promo.getState() === PROMO_STATE.EXPIRED || promo.isArchived()) {
        throw new Errors.ForbiddenToUpdatePromo();
    }
}

function validateCondition(condition: Condition): ValidationResult {
    if (condition.or && condition.and) {
        return {
            passed: false,
            failedMessage: "condition can't contain both 'and' and 'or' predicates"
        };
    }
    if (condition.or) { // has group
        if (condition.or.length <= 1) {
            return {
                passed: false,
                failedMessage: "condition group should contain at least two conditions"
            };
        } else {
            return validateConditions(condition.or);
        }
    }
    if (condition.and) { // has group
        if (condition.and.length <= 1) {
            return {
                passed: false,
                failedMessage: "condition group should contain at least two conditions"
            };
        } else {
            return validateConditions(condition.and);
        }
    }
    return {
        passed: true
    };
}

function validateConditions(conditions: Condition[]): ValidationResult {
    for (const condition of conditions) {
        const validationResult = validateCondition(condition);
        if (!validationResult.passed) {
            return validationResult;
        }
    }
    return {
        passed: true
    };
}

async function validateUpdatePromo(promoImpl: PromotionImpl,
                                   data: PromotionInfo,
                                   brand: BrandEntity): Promise<ValidationResult> {
    if (promoImpl.getState() === PROMO_STATE.IN_PROGRESS) {
        return validateUpdateRunningPromo(promoImpl, data);
    } else {
        if (promoImpl.isActive() && data.rewards) {
            return {
                passed: false,
                failedMessage: "cannot update rewards for active promo"
            };
        }
        if (data.status) {
            return {
                passed: false,
                failedMessage: "status can only be updated in corresponding request"
            };
        }
        return validatePromo(data, brand);
    }
}

function validateUpdateRunningPromo(promoImpl: PromotionImpl, data: PromotionInfo) {
    const isBns = promoImpl.getType() === PROMO_TYPE.BONUS_COIN;
    if (!data.endDate && !isBns) {
        return {
            passed: false,
            failedMessage: "Promo is in progress. Can update only end date"
        };
    }
    if (data.endDate) {
        if (isNaN(Date.parse(data.endDate))) {
            return {
                passed: false,
                failedMessage: "end date should be a datetime"
            };
        }

        const endDate = convertDateToUTC(new Date(data.endDate), promoImpl.getTimezone());
        if (endDate <= promoImpl.getStartDate()) {
            return {
                passed: false,
                failedMessage: "end date should be after start date"
            };
        }
    }
    if (isBns && !data.rewards && !data.endDate) {
        return {
            passed: false,
            failedMessage: "Promo is in progress. Can update only end date or rewards' expiration"
        };
    }
    if (data.rewards && isBns) {
        // update the Expiration time of the promotion to a longer value
        const existingRewards = promoImpl.getRewards();
        if (data.rewards.length !== existingRewards.length) {
            return {
                passed: false,
                failedMessage: "rewards count should be the same, we cannot delete or add new reward for running promo"
            };
        }
        let rewardsUpdated = false;
        for (let i = 0; i < data.rewards.length; i++) {
            const updatedReward = data.rewards[i] as ExpiringReward;
            const existingReward = existingRewards[i];
            const existingRewardInfo = existingReward.toInfo() as ExpiringReward;
            if (!existingRewardInfo.expirationPeriod) {
                return {
                    passed: false,
                    failedMessage: "we can update expiration only for rewards with expiration"
                };
            }

            const newExpirationPeriod = getRewardExpirationPeriod(updatedReward);
            const existingExpirationPeriod = getRewardExpirationPeriod(existingRewardInfo);
            if (existingExpirationPeriod > newExpirationPeriod) {
                return {
                    passed: false,
                    failedMessage: "we can only prolong expiration for reward"
                };
            }
            if (existingExpirationPeriod !== newExpirationPeriod) {
                rewardsUpdated = true;
            }
        }
        if (!data.endDate && !rewardsUpdated) {
            return {
                passed: false,
                failedMessage: "nothing to update"
            };
        }
    }

    return {
        passed: true
    };

}

async function validatePromo(data: PromotionInfo,
                             brand: BrandEntity,
                             isPromoCreate: boolean = false,
                             shouldStartImmediately: boolean = false): Promise<ValidationResult> {
    const shouldValidateStartDate = !shouldStartImmediately && (!data.startDate || isNaN(Date.parse(data.startDate)));
    if (shouldValidateStartDate || !data.endDate || isNaN(Date.parse(data.endDate))) {
        return {
            passed: false,
            failedMessage: "start and end dates should be a datetime"
        };
    }
    const startDate = shouldStartImmediately ? new Date() : convertDateToUTC(new Date(data.startDate), data.timezone);
    const endDate = convertDateToUTC(new Date(data.endDate), data.timezone);
    if (startDate >= endDate) {
        return {
            passed: false,
            failedMessage: "end date should be greater than the start date"
        };
    }

    if (data.conditions) {
        const validationResult: ValidationResult = validateCondition(data.conditions);
        if (!validationResult.passed) {
            return validationResult;
        }
    }

    if (isPromoCreate && !(data.rewards && data.rewards.length)) {
        return {
            passed: false,
            failedMessage: "rewards are missing"
        };
    }

    if (data.rewards) {
        if (!data.rewards.length) {
            return {
                passed: false,
                failedMessage: "can not set empty rewards"
            };
        }
        if (data.type === PROMO_TYPE.FREEBET) {
            return validateFreebetRewards(data.rewards as FreebetRewardInfo[], brand);
        } else if (data.type === PROMO_TYPE.BONUS_COIN) {
            return validateBonusCoinRewards(data.rewards as BonusCoinRewardInfo[], brand);
        }
    }

    return {
        passed: true
    };
}

async function validateFreebetPromo(data: FreebetSimplePromoInfo, brand: BrandEntity): Promise<ValidationResult> {
    if (isNaN(data.reward.freebetAmount)
        || data.reward.freebetAmount < 1
        || data.reward.freebetAmount > config.promo.maxFreebetsToAssign
        || (data.reward.freebetAmount % 1) !== 0) {
        return {
            passed: false,
            failedMessage: `freebetAmount must be integer between 1 and ${config.promo.maxFreebetsToAssign}`
        };
    }

    if (isNaN(Date.parse(data.startDate))) {
        return {
            passed: false,
            failedMessage: "start date should be a datetime"
        };
    }

    if (isNaN(Date.parse(data.endDate))) {
        return {
            passed: false,
            failedMessage: "end date should be a datetime"
        };
    }
    if (Date.parse(data.startDate) >= Date.parse(data.endDate)) {
        return {
            passed: false,
            failedMessage: "start date should be before end date"
        };
    }

    return validateFreebetRewards([data.reward], brand);
}

export async function updatePromoStatuses(entity: BrandEntity,
                                          promoIds: number[],
                                          status: string,
                                          userId: number): Promise<PromotionImpl[]> {
    if (promoIds.length > 1) {
        return Promise.reject(new Errors.ValidationError("Cannot update more than one promo at a time"));
    }

    const promo: PromotionImpl = await findOne({ brandId: entity.id, id: promoIds[0] });
    if (promo.hasFinished()) {
        return Promise.reject(new Errors.ForbiddenToUpdatePromo());
    }

    // nothing to change
    if (promo.getStatus() === status) {
        return [promo];
    }

    if (status === PROMO_STATUS.ACTIVE) {
        await PlayerRewardServices.get(entity, promo.getType()).activatePromo(promo, userId);
    } else {
        await PlayerRewardServices.get(entity, promo.getType()).inactivatePromo(promo, userId);
    }

    return [promo];
}

export async function updatePromoOwner(brand: BrandEntity,
                                       promoId: number,
                                       owner: string,
                                       userId: number): Promise<PromotionImpl> {
    const promo: PromotionImpl = await findOne({ brandId: brand.id, id: promoId });
    if (promo.getState() !== PROMO_STATE.PENDING) {
        return Promise.reject(new Errors.ForbiddenToUpdateNonPendingPromo());
    }

    // nothing to change
    if (promo.getOwner() === owner) {
        return promo;
    }

    promo.setUpdatedUser(userId)
        .setOwner(owner);

    return savePromo(promo);
}

export async function setPromosStarted(filter: WhereOptions<any>,
                                       doStart: (promo: any) => Promise<void>,
                                       userId?: number): Promise<PromotionImpl[]> {
    return db.transaction(async (transaction: Transaction): Promise<PromotionImpl[]> => {

        await ApplicationLock.lock(transaction, ApplicationLockId.START_PROMOTION);

        const promoDBInstances: PromotionDBInstance[] = await PromotionModel.findAll(
            {
                where: filter,
                transaction,
                include: [
                    {
                        model: PromotionFreebetRewardModel, as: "freebetRewards"
                    },
                    {
                        model: PromotionBonusCoinRewardModel, as: "bonusCoinRewards",
                    }
                ]
            });

        const promos = promoDBInstances.map((promoInstance) => new PromotionImpl(promoInstance));

        if (promos?.length) {
            log.info(`Start pending promotions size is ${promos.length}`);
        }

        for (const promo of promos) {
            await doStart(promo);

            promo.setUpdatedUser(userId);
            promo.setEverStarted(true);

            await savePromo(promo, transaction).catch((err) => {
                if (err instanceof OptimisticLockException) {
                    log.warn("Failed to save promo", `${err.code}. ${err.message}`);
                } else {
                    log.error("Failed to save promo with error", `${err.code}. ${err.message}`);
                }
                return Promise.reject(err);
            });
        }

        return promos;
    });
}

async function setPromoRewards(data: PromotionInfo,
                               promoType: string,
                               entity: BaseEntity,
                               transaction?: Transaction): Promise<PromotionRewardImpl[]> {
    await deletePromoRewards(data.id, transaction);
    switch (promoType) {
        case PROMO_TYPE.FREEBET:
            return createFreebetRewards(data.rewards as FreebetRewardInfo[], data.id, transaction);
        case PROMO_TYPE.BONUS_COIN:
            return createBonusCoinRewards(data.rewards as BonusCoinRewardInfo[], data.id, entity, transaction);
        default:
            await transaction.rollback();
            return Promise.reject(new Errors.ValidationError("Unknown promo type"));
    }
}

export async function deletePromo(promoId: number, transaction?: Transaction): Promise<any> {
    const destroyOptions: DestroyOptions = { where: { id: promoId } };
    if (transaction) {
        destroyOptions.transaction = transaction;
    }
    return PromotionModel.destroy(destroyOptions);
}

export async function archivePromo(promoId: number, entity: BaseEntity, userId: number): Promise<any> {
    const destructuredEGPPromoId = destructureEGPPromoId(promoId);
    if (destructuredEGPPromoId) {
        const { egpPromoId, gameProviderCode } = destructuredEGPPromoId;
        const { url } = getEGPConfiguration(gameProviderCode);
        return getEGPPromoGateway(url, gameProviderCode).removePromo(egpPromoId);
    }

    const promoDBInstance: PromotionDBInstance = await PromotionModel.findOne({ where: { id: promoId } });
    const bnsPromo: boolean = promoDBInstance.get("type") === PROMO_TYPE.BONUS_COIN;
    if (!promoDBInstance) {
        return Promise.reject(new Errors.PromotionNotFoundError());
    }
    await checkPromoAllowed(entity, true, bnsPromo);

    const promo = new PromotionImpl(promoDBInstance);
    if (promo.isRunning()) {
        return Promise.reject(new Errors.ForbiddenToUpdateNonPendingPromo());
    }

    promo.setArchived(true);
    promo.setUpdatedUser(userId);

    return savePromo(promo);
}

export function toShortInfo(array: PromotionImpl[], keyEntity?: BaseEntity): PromotionInfo[] {
    return array.map(promo => promo.toShortInfo(keyEntity));
}

export async function findReward<T extends PromotionRewardImpl>(promoId: number,
                                                                rewardId: number): Promise<[T, PromotionImpl]> {
    const promo = await findOne({ id: promoId });
    return [promo.getRewards().find((reward) => reward.getId() === rewardId) as any, promo];
}

export function getPromoPermissions(promoType: PROMO_TYPE,
                                    operation: PROMO_OPERATIONS,
                                    isKeyEntity: boolean): string[] {
    const permissions = [];
    let permissionPerType = MAP_PROMO_PERMISSIONS.get(promoType);
    if (isKeyEntity) {
        permissionPerType = `keyentity:${permissionPerType}`;
    }
    permissions.push(permissionPerType);

    if (operation === PROMO_OPERATIONS.CREATE || operation === PROMO_OPERATIONS.EDIT) {
        permissions.push(`${permissionPerType}:${operation}`);
    }

    return permissions;
}

function removeUnsupportedGamesFromRewards(data: PromotionInfo, validationResult: ValidationResult): void {
    if (!validationResult.unsupportedGames || !Object.keys(validationResult.unsupportedGames).length) {
        return;
    }
    const rewards: BonusCoinRewardInfo[] = data.rewards as BonusCoinRewardInfo[];
    rewards.forEach(r => r.games = r.games.filter(g => !validationResult.unsupportedGames[g]));
    log.warn(validationResult.unsupportedGames, "Unsupported games ignored");
}

export const queryParamsKeys = [
    "state",
    "status",
    "archived",
    "startDate",
    "endDate",
    "sortBy",
    "sortOrder",
    "offset",
    "limit",
    "title",
    "labelsId",
    "type",
    "owner",
    "externalId",
    "createdAt",
    "id",
];

const sortableKeys = [
    "startDate",
    "endDate",
    "title",
    "createdAt",
];
