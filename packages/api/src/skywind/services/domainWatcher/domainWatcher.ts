import { logging } from "@skywind-group/sw-utils";
import { getTapkingAdapter } from "./tapkingAdapter";
import { DomainSources, DomainWatcherAdapter, DomainStatus, DomainSource } from "./types";
import { DomainWatcherAdapterNotSupportedError } from "../../errors";
import { getDynamicDomainService, getStaticDomainService } from "../domain";
import { getStaticDomainPoolService } from "../staticDomainPool";
import { getDynamicDomainPoolService } from "../dynamicDomainPool";

interface DomainWatcherInfo {
    domainId?: number;
    status?: DomainStatus;
    sources?: DomainSource[];
}

export class DomainWatcher {
    private readonly domains: Map<string, DomainWatcherInfo> = new Map();

    constructor(private readonly adapter: DomainWatcherAdapter) {
    }

    public async update(domains: DomainSources, log?: logging.Logger) {
        const list = await this.adapter.list();
        for (const { domain, status } of list) {
            const existing = this.domains.get(domain);
            this.domains.set(domain, {
                domainId: existing?.domainId,
                status,
                sources: existing?.sources
            });
        }
        for (const domain of this.domains.keys()) {
            if (!domains.has(domain)) {
                await this.adapter.remove(domain);
                this.domains.delete(domain);
            }
        }
        for (const [domain, { domainId, sources }] of domains.entries()) {
            if (this.domains.has(domain)) {
                this.domains.set(domain, {
                    domainId,
                    status: this.domains.get(domain)!.status,
                    sources
                });
            } else {
                await this.adapter.register(domain);
                this.domains.set(domain, { domainId, status: { accessStatus: "UNKNOWN", lastCheckedAt: new Date() }, sources });
            }
        }
        for (const [domain, { domainId, status, sources }] of this.domains.entries()) {
            log?.info({ domain, domainId, status, sources }, "Set status %s for domain %s", status?.accessStatus, domain);
            if (domainId && sources?.length > 0) {
                for (const { type, domainId, poolId } of sources) {
                    if (["staticDomain", "staticPool"].includes(type)) {
                        log?.info({ domainId, status }, "Update staticDomain");
                        await getStaticDomainService().update({ id: domainId, info: { monitoringStatus: { [this.adapter.adapterId]: status } } });
                    }
                    if (["dynamicDomain", "dynamicPool"].includes(type)) {
                        log?.info({ domainId, status }, "Update dynamicDomain");
                        await getDynamicDomainService().update({ id: domainId, info: { monitoringStatus: { [this.adapter.adapterId]: status } } });
                    }
                    if (type === "staticPool" && status.accessStatus === "BLOCKED") {
                        log?.info({ poolId, domainId, status }, "Update staticPool");
                        await getStaticDomainPoolService().setBlocked(poolId, domainId, status.lastCheckedAt);
                    }
                    if (type === "dynamicPool" && status?.accessStatus === "BLOCKED") {
                        log?.info({ poolId, domainId, status }, "Update dynamicPool");
                        await getDynamicDomainPoolService().setBlocked(poolId, domainId, status.lastCheckedAt);
                    }
                }
                if (status?.accessStatus === "BLOCKED") {
                    await this.adapter.remove(domain);
                    this.domains.delete(domain);
                }
            }
        }
    }
}

function getAdapter(adapter: string, log?: logging.Logger): DomainWatcherAdapter {
    if (adapter === "tapking") {
        return getTapkingAdapter(log);
    }
    throw new DomainWatcherAdapterNotSupportedError(adapter);
}

export function getDomainWatcher(adapter: string, log?: logging.Logger): DomainWatcher {
    return new DomainWatcher(getAdapter(adapter, log));
}
