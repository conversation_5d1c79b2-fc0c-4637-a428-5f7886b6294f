import { CustomExchangeRate } from "@skywind-group/sw-currency-exchange";
import { DeferredPaymentMethod } from "@skywind-group/sw-deferred-payment";
import { Marketing } from "./game";
import { BrandFinalizationType, GGRCalculationType } from "@skywind-group/sw-wallet-adapter-core";

import { EntityStatus } from "./entity";

export interface GlobalSettings {
    userIdleTimeout: number;
    userLoginAttempts: number;
}

export interface GlobalSettingsUpdate {
    userIdleTimeout?: number;
    userLoginAttempts?: number;
}

export interface PromoSettings {
    sharedPromoEnabled?: boolean;
    minBonusCoinBet?: number;
    playerBonusesEnabled?: boolean;
}

/*
    This flags shows to GS what we should do with logout logic (https://jira.skywindgroup.com/browse/SWS-26469):
    1) gameClosure - for web socket games when we close the game and should ignore payments
        if it exists and send logout request immediately
    2) gameRelaunch - when player relaunch game with new token, we should send logout and
    and then send payment with new token
    3) offlineRetry - we should send logout request independently of result of broken payments (success or not)
 */
export interface LogoutControl {
    ignorePayments: {
        gameClosure: boolean;
        gameRelaunch: boolean;
        offlineRetry: boolean;
    };
}

export interface HistoryOptions {
    hideBalanceBeforeAndAfter?: boolean; // Remove balanceBefore/balanceAfter from reports if we have this settings on
}

interface CommonEntitySettings {
    restrictions?: EntityCurrencyRestrictions;
    ipWhitelist?: string[];
    boIpWhitelist?: string[];
    userIpWhitelist?: string[];
    urlParams?: {
        socketVersion?: string; // version of socket.io -> 2 or 4
        [key: string]: string;
    };
    maxTestPlayers?: number;
    passwordPattern?: string;
    isAccountBlockingEnabled?: boolean; // states whether an account should be blocked based on its balance
    twoFactorAuthSettings?: TwoFactorAuthSettings;
    isPasswordChangeEnabled?: boolean; // indicates that user will be forced to change password on first login
    isPlayerPasswordChangeEnabled?: boolean | null; // indicates that player will be forced to change password on first
                                                    // login
    isPasswordForceChangeEnabled?: boolean; // if user will be forced to change password every specified period of time
    passwordForceChangePeriod?: number; // period of days in which password is valid and not require change
    maintenanceUrl?: string;
    virtualCurrencyRate?: CustomExchangeRate;
    responsibleGaming?: EntityResponsibleGamingSettings;
    validationSettings?: ValidationSettings;
    storePlayerInfo?: boolean;
    contributionPrecision?: number; // Value precision: Number of digits after dot.
    splitPayment?: boolean; // Indicates that games transactions bet + win must be split on 2 phases bet and win.
    maxPaymentRetryAttempts?: number; // Indicates how many retry should be make by games server
    minPaymentRetryTimeout?: number; // Indicates when to start payment retransmissions
    isPlayerCodeUniqueInSubtree?: boolean; // indicates that player code shall be unique under the whole structure
    uniqueEntityNamesInSubtree?: boolean; // entity name shall be unique under the whole structure
    uniqueUsernamesInSubtree?: boolean; // username shall be unique under the whole structure
    autoAddNewGamesToGroups?: boolean;
    dynamicRoutingEnabled?: boolean; // indicates that dynamic domain will be selected per player location
    autoPlaySettings?: AutoPlaySetting[];
    // per game splash overriding. specify if need to override 'splash' value from urlParams for specific game
    gameSplashes?: { [key: string]: string; };
    jpTickerRefreshPeriod?: number;
    rtpDeductionFunModeEnabled?: boolean; // indicates if RTP deduction is enabled in 'fun' mode
    marketing?: Marketing; // contributions to marketing jackpots
    newLimitsEnabled?: boolean; // Indicates whether new limits are enabled
    replayEnabled?: boolean; // Enables returning the replay url in rounds history, if supported by the game
    clientFeatures?: ClientFeatures;
    // For moorgate. If flag enabled validation of game limits partially disabled
    allowToOverrideDefaultLimits?: boolean;
    autoCreateTestJackpot?: boolean; // Auto create test jackpot instances

    ghAppSettings?: GHAppSettings;

    roundExpireAt?: number; // Time when round context should be expired (in minutes)
    finalizationRetryPolicy?: {
        factor?: number;
        maxRetries?: number;
        initialRetryTimeout?: number;
    };

    defaultFunGameBalance?: number;
    enablePhantomFeatures?: boolean;
    checkWebSiteWhitelisted?: boolean;
    isMarketplaceSupported?: boolean; // indicates that entity games supports marketplace
    deferredContribution?: boolean; // Should be true if you need contribution amount before bet payment
    blockGameLaunchForEmptyReferrer?: boolean; // Is used for website whitelisted check

    markPlayersAsTest?: boolean;
    gameGroupsInheritance?: boolean;
    regulatoryLinks?: RegulatoryLink[];
    allowedStaticDomainsForChildId?: number[]; // Restriction for child entities
    defaultGameGroup?: string;
    dividedSettings?: DividedEntitySettings;
    denyBoSimultaneousLogin?: boolean;
    finalizationSupport?: BrandFinalizationType;
    finalizeOnOfflineWin?: boolean;
    isForceFinishInsteadOfFinalizationSupported?: boolean;
    skipPendingPaymentReAuthentication?: boolean;
    addBetAmountOnFreeBetRollback?: boolean;
    omitBnsBalance?: boolean;
    useMerchantPlayerGameGroup?: boolean;
    disableTransferOutForOnlinePlayers?: boolean;
    bonusPaymentMethod?: DeferredPaymentMethod;
    newDeferredPaymentPromoId?: boolean;
    skywindHistory?: boolean; // Default behavior is operator history in priority
    ggrCalculation?: GGRCalculationType;
    // when true, slot-engine will check game.isBonusFeatureMode state prior to closing a round
    disableCloseRoundForBonusMode?: boolean;
    isBetaBO?: boolean;
    gamble?: boolean;
    /**
     * When player does not wager within X round system should block his chat privileges
     */
    numberOfRoundsWithoutBets?: number;
    /**
     * When player lost all the balance and his balance < 1 EUR after Y rounds his chat privileges are disabled.
     */
    numberOfRoundsWithoutBalance?: number;

    logoutControl?: LogoutControl;

    /*
     * This flag will explicitly mark this entity as the one for playing operator's bonus money.
     * Can be assigned only to test entity.
     */
    bonusPlayEntity?: boolean;

    restrictedCountries?: string[];
    useCountriesFromJurisdiction?: boolean; // Indicates whether we should use allowed/restricted countries or default
                                            // country from Jurisdiction for validations

    playerPrefix?: string;
    limitFeaturesToMaxTotalStake?: boolean;
    launchGameInsideLobby?: boolean;
    supportedBonusPaymentMethod?: string;
    supportHistoryUrl?: boolean;
    addDecodedRoundIdToHistory?: boolean;

    playerPrefixEnabled?: boolean;

    env?: string;

    flatReportsEnabled?: boolean; // Indicates that flat reports are enabled for specific entity and its children
    smResultEnabled?: boolean; // Indicates whether smResult calculation is enabled for a specific operator
    phantomJackpotApiUrl?: string; // URL for Phantom API
    defaultStatusForNewEntity?: EntityStatus;
    uboShowOnlyAnalytics?: boolean; // Indicates whether to show only Analytics tabs in SW BO (SkyWind Back Office)

    useGameProviderLimits?: boolean; // Indicates that limits will send as empty object (external limits will be used)
    gameProviderSiteCodes?: GameProviderSiteCodes;
    gameProviderJackpotSettings?: GameProviderJackpotSettings;

    hashParams?: string[];
    hashLobbyAndCashierEnabled?: boolean; // Indicates that hashes of lobby and cashier will be added to startGameToken

    merchantGameRestrictionsUseIpCountries?: string[]; // List of country codes for merchant game restriction
    restrictedIpCountries?: EntityCountryRestrictions;

    newExternalBetWinHistoryEnabled?: boolean; // Indicates that external bet win history works via Redis-Postgres

    gameLimitsSettings?: Record<string, {
        toEURMultiplier?: number;
        copyLimitsFrom?: string;
    }>;

    throwErrorOnDuplicateTransaction?: boolean;

    currencyFormatSettings?: CurrencyFormatSettings;

    social?: boolean;
    useSocialCasinoOperator?: string;

    isFunModeNotSupported?: boolean;

    wrapperLauncherVersion?: string;

    tableauBaseUrl?: string;
    tableauTrustServerUrl?: string;

    biVersion?: BiVersion;

    jpnUrl?: string;
    jpnTickerUrl?: string;

    forcePendingRewards?: boolean;
    cacheMergedEntitySettings?: boolean;
    cacheEntityGames?: boolean;

    skipPlayerInfoRequest?: boolean;
    skipAvailableSiteRequest?: boolean;
    skipBlockedPlayerValidation?: boolean;
    skipTestPlayerValidation?: boolean;

    useRemoteWallet?: boolean;
}

export enum BiVersion {
    LOCAL = "local",
    GLOBAL = "global",
    BOTH = "both"
}

export interface RegulatoryLink {
    urlType: string;
    url: string;
}

export interface ClientFeatures {
    fastPlay?: boolean; // Triggered the next spin within less than 1 second of all reels stopping.
    turboPlus?: boolean; // There is an additional faster mode. Spin time of Turbo+ mode will be shorter than in Turbo.
    turbo?: boolean; // Is Turbo button enabled or disabled by default. (true - enabled)
    fullscreen?: boolean;
    allowOppositeBets?: boolean; // to disable opposite betting for other games
    tableFullCoverage?: boolean; // to disable opposite betting for Roulette/Rush
    showRTPOverrideJrsd?: boolean; // provide RTP values to client per operator if jrsdSettings contains showRTP=false
    maxBuyInStake?: number; // max amount for buy in stake in EUR
    maxAnteBetStake?: number; // max amount for ante bet stake in EUR
}

export interface GHAppSettings {
    urlParams?: {
        showAdditionalInfo: boolean
    };
}

interface RoundDetailsSettings {
    roundDetailsReportEnabled?: boolean;
}

interface FixedExtraDataSettings {
    fixedExtraData?: {
        extRoundId: string;
        regulatoryData: {
            aamsSessionCode: string;
            participationStartDate: string;
            ropCode: string;
        }
    };
}

export interface EntitySettingsUpdate extends CommonEntitySettings,
    PromoSettings, HistoryOptions, RoundDetailsSettings, FixedExtraDataSettings {
    templates?: EmailTemplates; // deprecated
    emailTemplates?: EmailTemplates;
}

export interface EntitySettings extends EntitySettingsUpdate {
    emailTemplates: EmailTemplates;
}

export interface DividedEntitySettings {
    parent: CommonEntitySettings;
    own: CommonEntitySettings;
}

export interface EmailTemplates {
    passwordRecovery: EmailTemplate;
    changeEmail: EmailTemplate;
}

export interface EmailTemplate {
    from: string;
    subject: string;
    html: string;
}

export interface EntityCurrencyRestrictions {
    countries?: {
        [countryCode: string]: string[];
    };
    ignore?: boolean;
}

export interface EntityCountryRestrictions {
    countries?: string[];
    ignore?: true;
}

export interface ValidationSettings {
    playerCodeLength: {
        min: number;
        max: number;
    };
}

export interface TwoFactorAuthSettings {
    isAuthEnabled?: boolean;
    authOptions?: string[];
    smsTemplates?: { [key: string]: string; default?: string; defaultEnTemplateForBO?: string };
    mailTemplates?: { [key: string]: EmailTemplate; default?: EmailTemplate; };
}

export const MAX_TEST_PLAYERS_DEFAULT: number = 10;

export interface EntityResponsibleGamingSettings {
    enabled: boolean;
    jurisdiction?: string;
}

export interface EntitiesSettings {
    [key: string]: EntitySettings;
}

export interface AutoPlaySetting {
    label: string;
    value: number;
    isUntilFeature?: boolean;
    isDefault?: boolean;
}

export interface GameProviderSiteCodes {
    [gameProviderCode: string]: string;
}

export interface GameProviderJackpotSettings {
    [gameProviderCode: string]: {
        widgetCode?: string;
        jpId?: string;
        [key: string]: any;
    };
}

export interface CurrencyFormatSettings {
    [currencyCode: string]: CurrencyFormatConfig;
}

export const enum DecimalPartAppendType {
    CUT_ZERO_ONLY,
    CUT_DECIMAL,
    APPEND_DECIMAL,
    CUT_INSIGNIFICANT_ZERO,
}

export interface CurrencyFormatConfig {
    /** Whether it's needed or not to show currency sign. */
    showCurrency?: boolean;
    /** Indicates if we should hide currency sign in Fun mode by adding { showCurrency: false } */
    hideCurrencyInFunMode?: boolean;
    /** Decimal part separator character (example: 1.00) */
    decimalPartSeparatorCharacter?: string;
    /** Decimal part default digits count (example: 10.00) */
    decimalPartDigitsCount?: number;
    /** Real part separator character (example: 100,000.00) */
    realPartSeparatorCharacter?: string;
    /** Split number real part with comma by defined digits count, processed from right to left (example: 1,000,000) */
    realPartSeparationDigitsCount?: number;
    /** Append currency symbol to left or right */
    appendCurrencyToLeft?: boolean;
    /** Enables short mode postfixes K:10^3, M:10^6, B:10^9 ( example $1.45K or $9.99M ) */
    shortMode?: boolean;
    /** Use decimals type for tickups the same as we use for the current line bet value. */
    tickupDecimalsAsLineBet?: boolean;
    /** Sets displaying type of decimal part */
    decimalPartAppendType?: DecimalPartAppendType;
}
