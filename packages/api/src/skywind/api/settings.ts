import { NextFunction, Request, Response, Router } from "express";
import {
    auditable,
    authenticate,
    authorize,
    decodePid,
    getBooleanParamFromRequestQuery,
    getEntity,
    validate
} from "./middleware/middleware";
import { KeyEntityHolder } from "../services/security";
import * as Errors from "../errors";
import { ValidationError } from "../errors";
import * as Settings from "../services/settings";
import EntitySettingsService, {
    BO_WHITELIST_KEY,
    getErrorsOfValidationSettings,
    IP_WHITELIST_KEY,
    USER_WHITELIST_KEY
} from "../services/settings";
import { EntityInfoService } from "../services/entityInfo";
import {
    BiVersion,
    EmailTemplate,
    EntitySettings,
    EntitySettingsUpdate,
    GlobalSettingsUpdate
} from "../entities/settings";
import { BaseEntity, ChildEntity, Entity, EntityStatus } from "../entities/entity";
import { COUNTRIES, isObject, SOCKET_VERSION, TWO_FA_TYPE, TWO_FA_TYPE_ARR } from "../utils/common";
import IpWhiteListService, { validateIpList } from "../services/ipWhitelist";
import { Currencies } from "@skywind-group/sw-currency-exchange";
import { customValidators } from "./middleware/validatorMiddleware";
import { validateMarketingContributions } from "../services/game";
import { getStaticDomainService } from "../services/domain";
import { DeferredPaymentMethod } from "@skywind-group/sw-deferred-payment";
import { BrandFinalizationType, GGRCalculationType, } from "@skywind-group/sw-wallet-adapter-core";
import BoWhiteListService from "../services/boWhiteList";
import UserWhiteListService from "../services/userWhiteList";
import { isURL } from "validator";
import { Op } from "sequelize";

const router: Router = Router();

const isBoolean = (value) => value === undefined || value === null || typeof value === "boolean";
const isEmailTemplate = (template: EmailTemplate) => template?.from && template?.subject && template?.html;
const booleanFields: string[] = [
    "storePlayerInfo",
    "isAccountBlockingEnabled",
    "splitPayment",
    "isPlayerCodeUniqueInSubtree",
    "uniqueEntityNamesInSubtree",
    "uniqueUsernamesInSubtree",
    "dynamicRoutingEnabled",
    "isPasswordChangeEnabled",
    "isPasswordForceChangeEnabled",
    "newLimitsEnabled",
    "fastPlay",
    "turboPlus",
    "turbo",
    "allowToOverrideDefaultLimits",
    "sharedPromoEnabled",
    "isMarketplaceSupported",
    "deferredContribution",
    "markPlayersAsTest",
    "gameGroupsInheritance",
    "denyBoSimultaneousLogin",
    "skywindHistory",
    "gamble",
    "hideBalanceBeforeAndAfter",
    "allowOppositeBets",
    "tableFullCoverage",
    "newDeferredPaymentPromoId",
    "limitFeaturesToMaxTotalStake",
    "launchGameInsideLobby",
    "supportHistoryUrl",
    "addDecodedRoundIdToHistory",
    "playerPrefixEnabled",
    "uboShowOnlyAnalytics"
];

const JP_TICKER_REFRESH_MAX_PERIOD = 60 * 60 * 24; // seconds in 1 day

/**
 * View global settings.
 */
router.get("/globalSettings",
    authenticate,
    authorize,
    async (req: Request & KeyEntityHolder, res: Response, next: NextFunction) => {
        if (!req.keyEntity.isMaster()) {
            return next(new Errors.NotMasterEntityError());
        }
        try {
            res.send(await Settings.getGlobalSettings());
            next();
        } catch (err) {
            next(err);
        }
    });

/**
 * Update global settings.
 */
router.patch("/globalSettings",
    authenticate,
    authorize,
    auditable,
    async (req: Request & KeyEntityHolder, res: Response, next: NextFunction) => {
        if (!req.keyEntity.isMaster()) {
            return next(new Errors.NotMasterEntityError());
        }
        const update: GlobalSettingsUpdate = req.body;
        try {
            res.send(await Settings.updateGlobalSettings(update));
            next();
        } catch (err) {
            next(err);
        }
    });

/**
 * Reset global settings to defaults.
 */
router.delete("/globalSettings",
    authenticate,
    authorize,
    auditable,
    async (req: Request & KeyEntityHolder, res: Response, next: NextFunction) => {
        if (!req.keyEntity.isMaster()) {
            return next(new Errors.NotMasterEntityError());
        }
        try {
            res.send(await Settings.resetGlobalSettings());
            next();
        } catch (err) {
            next(err);
        }
    });

/**
 * View entity settings.
 */
export async function getEntitySettings(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const entity = getEntity(req, { ignoreSuspended: req.keyEntity.isMaster() });
        res.send(await Settings.getEntitySettings(entity.path,
            getBooleanParamFromRequestQuery(req, "onlyOwnSettings"),
            getBooleanParamFromRequestQuery(req, "extendSettings")));
        next();
    } catch (err) {
        next(err);
    }
}

export async function validateEntitySettings(entity: BaseEntity, update: EntitySettingsUpdate) {
    const errors: string[] = [];

    if (entity.isMaster() && update.emailTemplates === null) {
        errors.push("emailTemplates setting is required for master");
    }

    if (update.numberOfRoundsWithoutBets !== undefined &&
        (!Number.isInteger(update.numberOfRoundsWithoutBets) || update.numberOfRoundsWithoutBets < 0)) {
        errors.push("numberOfRoundsWithoutBets should be a positive integer");
    }
    if (update.numberOfRoundsWithoutBalance !== undefined &&
        (!Number.isInteger(update.numberOfRoundsWithoutBalance) || update.numberOfRoundsWithoutBalance < 0)) {
        errors.push("numberOfRoundsWithoutBalance should be a positive integer");
    }
    if (update.passwordPattern && !isValidRegex(update.passwordPattern)) {
        errors.push("password validator is invalid");
    }

    if (update.emailTemplates) {
        if (!update.emailTemplates.passwordRecovery) {
            errors.push("templates.passwordRecovery is required");
        } else {
            if (!isEmailTemplate(update.emailTemplates.passwordRecovery)) {
                errors.push("emailTemplates.passwordRecovery.[from|subject|html] is required");
            }
        }

        if (update.emailTemplates.changeEmail) {
            if (!isEmailTemplate(update.emailTemplates.changeEmail)) {
                errors.push("emailTemplates.changeEmail.[from|subject|html] is required");
            }
        }
    }

    if (update?.restrictions?.countries) {
        for (const country of Object.keys(update.restrictions.countries)) {
            if (!COUNTRIES[country]) {
                errors.push("country " + country + " doesn't exist");
            }
            for (const currency of update.restrictions.countries[country]) {
                if (!Currencies.exists(currency)) {
                    errors.push("currency " + currency + " doesn't exist");
                }
            }
        }
    }

    if (update.restrictedCountries) {
        if (Array.isArray(update.restrictedCountries)) {
            for (const country of update.restrictedCountries) {
                if (!COUNTRIES[country]) {
                    errors.push(`${country} should be valid ISO country code`);
                }
            }
            if (update.restrictedCountries.includes((entity as any).defaultCountry)) {
                errors.push("Restricted country list cannot contain default country");
            }
        } else {
            errors.push("Restricted countries should be an array");
        }
    }

    if (update[IP_WHITELIST_KEY]) {
        errors.concat(validateIpList(update[IP_WHITELIST_KEY], IP_WHITELIST_KEY));
    }
    if (update[BO_WHITELIST_KEY]) {
        errors.concat(validateIpList(update[BO_WHITELIST_KEY], BO_WHITELIST_KEY));
    }
    if (update[USER_WHITELIST_KEY]) {
        errors.concat(validateIpList(update[USER_WHITELIST_KEY], USER_WHITELIST_KEY));
    }

    if (update.maxTestPlayers !== undefined &&
        (!Number.isInteger(update.maxTestPlayers) || update.maxTestPlayers < 0)) {
        errors.push("maxTestPlayers should be a positive integer");
    }

    const TFASettings = update.twoFactorAuthSettings;
    if (TFASettings) {
        if (!Array.isArray(TFASettings.authOptions)) {
            errors.push("twoFactorAuthSettings.authOptions should be a list");
        } else {
            if (TFASettings.authOptions.some(item => TWO_FA_TYPE_ARR.indexOf(item) < 0)) {
                errors.push("unknown two factor auth type");
            }

            if (TFASettings.isAuthEnabled &&
                TFASettings.authOptions.indexOf(TWO_FA_TYPE.EMAIL) !== -1 &&
                (!TFASettings.mailTemplates || !TFASettings.mailTemplates.default)) {
                errors.push("mail template for second step auth should be specified");
            }
        }
    }

    booleanFields.forEach(booleanField => {
        if (!isBoolean(update[booleanField])) {
            errors.push(`${booleanField} should be boolean`);
        }
    });

    if (update?.isPlayerPasswordChangeEnabled) {
        if (!isBoolean(update.isPlayerPasswordChangeEnabled)) {
            errors.push("isPlayerPasswordChangeEnabled should be boolean");
        }
    }

    if (update.maintenanceUrl !== undefined && update.maintenanceUrl !== null
        && !customValidators.isValidUrl(update.maintenanceUrl)) {
        errors.push("maintenanceUrl should be URL");
    }

    if (update.virtualCurrencyRate) {
        for (const baseCurrency of Object.keys(update.virtualCurrencyRate)) {
            if (!Currencies.exists(baseCurrency)) {
                errors.push(`currency ${baseCurrency} doesn't exist`);
            } else if (!Currencies.get(baseCurrency).isVirtual) {
                errors.push(`currency ${baseCurrency} is not virtual`);
            } else {
                for (const targetCurrency of Object.keys(update.virtualCurrencyRate[baseCurrency])) {
                    if (!Currencies.exists(targetCurrency)) {
                        errors.push(`currency ${targetCurrency} doesn't exist`);
                    }
                    const rate = update.virtualCurrencyRate[baseCurrency][targetCurrency];
                    if (typeof rate !== "number" || rate <= 0) {
                        errors.push("virtualCurrencyRate should be a positive number");
                    }
                }
            }
        }
    }

    if (update.responsibleGaming && update.responsibleGaming.enabled && !update.responsibleGaming.jurisdiction) {
        errors.push("Enabled responsible gaming must have a jurisdiction");
    }

    if (update.contributionPrecision && !Number.isInteger(update.contributionPrecision)) {
        errors.push("Invalid contributionPrecision value");
    }

    if (update.jpTickerRefreshPeriod) {
        if (!Number.isInteger(update.jpTickerRefreshPeriod)) {
            errors.push("Invalid jpTickerRefreshPeriod value");
        } else if (update.jpTickerRefreshPeriod < 1 || update.jpTickerRefreshPeriod > JP_TICKER_REFRESH_MAX_PERIOD) {
            errors.push(`jpTickerRefreshPeriod should be in range [1..${JP_TICKER_REFRESH_MAX_PERIOD}] seconds.`);
        }
    }

    if ((update.passwordForceChangePeriod || 0 === update.passwordForceChangePeriod) &&
        (!Number.isInteger(update.passwordForceChangePeriod) || update.passwordForceChangePeriod <= 0)) {
        errors.push("passwordForceChangePeriod should be positive integer");
    }

    if (update.minPaymentRetryTimeout !== undefined
        && update.minPaymentRetryTimeout !== null
        && (!Number.isInteger(update.minPaymentRetryTimeout) || update.minPaymentRetryTimeout < 30)) {
        errors.push("minPaymentRetryTimeout should be positive integer greater or equals to 30");
    }

    if (update.ghAppSettings) {
        const showAdditionalInfo = update.ghAppSettings?.urlParams?.showAdditionalInfo;
        if (showAdditionalInfo === undefined || !isBoolean(showAdditionalInfo)) {
            errors.push("ghAppSettings.urlParams.swAdditionalInfo should be a boolean");
        }
    }

    if (update.urlParams) {
        const urlParams = update.urlParams;
        if (urlParams.splash && typeof urlParams.splash !== "string") {
            errors.push("urlParams.splash should be a string");
        }
        if (!isValidURL(urlParams.cashier)) {
            errors.push("urlParams.cashier should be a URL");
        }
        if (!isValidURL(urlParams.lobby)) {
            errors.push("urlParams.lobby should be a URL");
        }
        if (!isValidURL(urlParams.history_url)) {
            errors.push("urlParams.history_url should be a URL");
        }
        if (!isValidURL(urlParams.history2_url)) {
            errors.push("urlParams.history2_url should be a URL");
        }
        if (urlParams.disableBalancePing && typeof urlParams.disableBalancePing !== "boolean") {
            errors.push("urlParams.disableBalancePing should be a boolean");
        }
        if (urlParams.socketVersion && !SOCKET_VERSION[urlParams.socketVersion]) {
            errors.push(`urlParams.socketVersion should be a ${Object.keys(SOCKET_VERSION).join(" or ")}`);
        }
    }

    if (update.roundExpireAt !== undefined && update.roundExpireAt !== null &&
        (!Number.isInteger(update.roundExpireAt) || update.roundExpireAt <= 0)) {
        errors.push("roundExpireAt should be a positive integer greater than 1");
    }

    if (update.defaultGameGroup !== null && update.defaultGameGroup !== undefined && typeof update.defaultGameGroup !== "string") {
        errors.push("defaultGameGroup should be string");
    }

    await validateStaticDomainsForChild(entity, update.allowedStaticDomainsForChildId, errors);

    const result = getErrorsOfValidationSettings(update);
    if (result) {
        errors.push(result);
    }

    if (update.finalizationSupport !== undefined && update.finalizationSupport !== null) {
        const types: string[] = Object.keys(BrandFinalizationType).map(k => BrandFinalizationType[k]);
        if (!types.includes(update.finalizationSupport)) {
            errors.push(`finalizationSupport should one from ${types}`);
        }
    }

    if (update.bonusPaymentMethod !== undefined && update.bonusPaymentMethod !== null) {
        const types: string[] = Object.keys(DeferredPaymentMethod).map(k => DeferredPaymentMethod[k]);
        if (!types.includes(update.bonusPaymentMethod)) {
            errors.push(`bonusPaymentMethod should one from ${types}`);
        }
    }

    if (update.supportedBonusPaymentMethod !== undefined && update.supportedBonusPaymentMethod !== null) {
        const types = ["manual", "credit", "bonus"];
        if (!types.includes(update.supportedBonusPaymentMethod)) {
            errors.push(`supportedBonusPaymentMethod should one from ${types}`);
        }
    }

    if (update.ggrCalculation !== undefined && update.ggrCalculation !== null) {
        const types: string[] = Object.keys(GGRCalculationType).map(k => GGRCalculationType[k]);
        if (!types.includes(update.ggrCalculation)) {
            errors.push(`ggrCalculation should one from ${types}`);
        }
    }

    if (update.logoutControl !== undefined && update.logoutControl !== null) {
        const fields = ["gameClosure", "gameRelaunch", "offlineRetry"];
        for (const field of fields) {
            const value = update?.logoutControl?.ignorePayments?.[field as any];

            if (!isBoolean(value)) {
                errors.push(`${field} should be boolean`);
            }
        }
    }

    if (update.defaultStatusForNewEntity) {
        const entityStatuses = Object.entries(EntityStatus).map(status => status[1]);
        if (!entityStatuses.includes(update.defaultStatusForNewEntity)) {
            errors.push(`defaultStatusForNewEntity can take only these values: ${entityStatuses.join(", ")}`);
        }
    }

    const gameLimitsSettings = update.gameLimitsSettings;
    if (gameLimitsSettings !== undefined && gameLimitsSettings !== null) {
        if (!isObject(gameLimitsSettings)) {
            errors.push("gameLimitsSettings should be object");
        } else {
            const fields = ["toEURMultiplier", "copyLimitsFrom"];
            for (const [currency, item] of Object.entries(gameLimitsSettings)) {
                if (!Currencies.exists(currency)) {
                    errors.push(`gameLimitsSettings - ${currency} doesn't exist`);
                }
                if (!isObject(item)) {
                    errors.push(`gameLimitsSettings[${currency}] - ${typeof item} should be object`);
                }
                for (const name of Object.keys(item)) {
                    if (!fields.includes(name)) {
                        errors.push(`gameLimitsSettings[${currency}].${name} should be one of [${fields.join(", ")}]`);
                    }
                }
                const { toEURMultiplier, copyLimitsFrom } = item;
                if (toEURMultiplier && !Number.isInteger(toEURMultiplier) || toEURMultiplier < 0) {
                    errors.push(`gameLimitsSettings[${currency}].toEURMultiplier ${toEURMultiplier} should be a positive integer`);
                }
                if (copyLimitsFrom && !Currencies.exists(copyLimitsFrom)) {
                    errors.push(`gameLimitsSettings[${currency}].copyLimitsFrom ${copyLimitsFrom} doesn't exist`);
                }
            }
        }
    }

    if (update?.biVersion && !Object.values(BiVersion).includes(update?.biVersion)) {
        errors.push(`biVersion should be omitted or one of following values: ${Object.values(BiVersion).join(", ")}`);
    }

    if (errors.length) {
        throw new Errors.ValidationError(errors);
    }

    const entitySettings = await Settings.getEntitySettings(entity.path);
    await validateMarketingContributions(update.marketing, entitySettings);

    return errors;
}

async function validateStaticDomainsForChild(entity: BaseEntity, allowedStaticDomainsForChildId: number[],
                                             errors: string[]): Promise<void> {
    if (allowedStaticDomainsForChildId) {
        if (!Array.isArray(allowedStaticDomainsForChildId) || allowedStaticDomainsForChildId.length === 0) {
            errors.push("allowedStaticDomainsForChildId should be not empty array");
            return;
        }
        const uniqueIds = [...new Set(allowedStaticDomainsForChildId)];
        if (!entity.isMaster()) {
            const parentSettings = await Settings.getEntitySettings((entity as ChildEntity).getParent().path);
            const allowedParentDomains = parentSettings.allowedStaticDomainsForChildId;
            if (allowedParentDomains) {
                const newDomains = allowedStaticDomainsForChildId.filter(d => allowedParentDomains.indexOf(d) === -1);
                if (newDomains.length > 0) {
                    errors.push(`Static domains ${newDomains} are missing in parent settings`);
                    return;
                }
            } else {
                await validateIfStaticDomainsExist(uniqueIds, errors);
            }
        } else {
            await validateIfStaticDomainsExist(uniqueIds, errors);
        }
        if ((entity as Entity).child && (entity as Entity).child.length) {
            const currentSettings = await Settings.getEntitySettings(entity.path, true);
            const currentDomains = currentSettings.allowedStaticDomainsForChildId;
            if (currentDomains && currentDomains.length) {
                const domainsToRemove = new Set(currentDomains
                    .filter(d => allowedStaticDomainsForChildId.indexOf(d) === -1));
                if (domainsToRemove.size) {
                    await checkIfStaticDomainsInChildSettings(entity as Entity, domainsToRemove);
                }
            }
        }
    }
}

async function validateIfStaticDomainsExist(domains: number[], errors: string[]): Promise<void> {
    const domainService = getStaticDomainService();
    const existingIdsCount = await domainService.findAll({
        attributes: { include: ["id"] },
        where: { id: { [Op.in]: domains } }
    });
    if (existingIdsCount.length !== domains.length) {
        errors.push("Some of static domains are not exist");
    }
}

async function checkIfStaticDomainsInChildSettings(entity: Entity, domains: Set<number>): Promise<void> {
    if (entity.child && entity.child.length) {
        for (const c of entity.child) {
            const settings = await Settings.getEntitySettings(c.path, true);
            const childDomains = settings.allowedStaticDomainsForChildId;
            if (childDomains) {
                childDomains.forEach(d => {
                    if (domains.has(d)) {
                        throw new ValidationError(`Domain ${d} cannot be removed because it used in ${c.name}`);
                    }
                });
            }
            await checkIfStaticDomainsInChildSettings(c as Entity, domains);
        }
    }
}

/**
 * Update entity settings
 */
const patchSettings = async (req: Request & KeyEntityHolder, res: Response, next: NextFunction) => {
    const settings: EntitySettingsUpdate = req.body;
    try {
        const entity = getEntity(req, { ignoreSuspended: req.keyEntity.isMaster() });
        await validateEntitySettings(entity, settings);

        const settingsService = new EntitySettingsService(entity);
        await settingsService.validate(settings);

        if (settings[BO_WHITELIST_KEY]) {
            const whiteListService = new BoWhiteListService(entity);
            settings[BO_WHITELIST_KEY] = await whiteListService.createOrUpdate(settings[BO_WHITELIST_KEY]);
        }
        if (settings[USER_WHITELIST_KEY]) {
            const whiteListService = new UserWhiteListService(entity);
            settings[BO_WHITELIST_KEY] = await whiteListService.createOrUpdate(settings[USER_WHITELIST_KEY]);
        }

        res.send(await settingsService.patch(settings));
    } catch (err) {
        next(err);
    }
};

const patchSettingsWithRestrictedCountriesSolution = async (req: Request & KeyEntityHolder,
                                                            res: Response,
                                                            next: NextFunction) => {
    const useCountriesFromJurisdiction = req.body.useCountriesFromJurisdiction;

    try {
        if (!("useCountriesFromJurisdiction" in req.body) || !isBoolean(useCountriesFromJurisdiction)) {
            throw new Errors.ValidationError("useCountriesFromJurisdiction is required and should have boolean value");
        }

        const entity = getEntity(req, { ignoreSuspended: req.keyEntity.isMaster() });
        const settingsService = new EntitySettingsService(entity);

        res.send(await settingsService.patch({ useCountriesFromJurisdiction }));
    } catch (err) {
        next(err);
    }
};

const putSettings = async (req: Request & KeyEntityHolder, res: Response, next: NextFunction) => {
    const settings: EntitySettings = req.body;
    try {
        const entity = getEntity(req, { ignoreSuspended: req.keyEntity.isMaster() });

        await validateEntitySettings(entity, settings);

        const settingsService = new EntitySettingsService(entity);
        await settingsService.validate(settings);

        if (settings[BO_WHITELIST_KEY]) {
            const whiteListService = new BoWhiteListService(entity);
            settings[BO_WHITELIST_KEY] = await whiteListService.createOrUpdate(settings[BO_WHITELIST_KEY], false);
        }
        if (settings[USER_WHITELIST_KEY]) {
            const whiteListService = new UserWhiteListService(entity);
            settings[USER_WHITELIST_KEY] = await whiteListService.createOrUpdate(settings[USER_WHITELIST_KEY], false);
        }

        res.send(await settingsService.update(settings));
    } catch (err) {
        next(err);
    }
};

function isValidRegex(regex): boolean {
    let isValid = true;
    try {
        new RegExp(regex);
    } catch (e) {
        isValid = false;
    }
    return isValid;
}

function isValidURL(value: any): boolean {
    if (value === undefined || value === null || value === 0) {
        return true;
    }
    if (typeof value === "string") {
        if (value.length === 0 || value === "0") {
            return true;
        }
        const url = value.replace(/{/g, "").replace(/}/g, "");
        return isURL(url, { require_tld: false });
    }
    return false;
}

/**
 * Reset entity settings to defaults.
 */
async function resetEntitySettings(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const entity = getEntity(req);

        if (entity.isMaster()) {
            return next(new Errors.ValidationError("cannot reset settings for master"));
        }

        const settings = await (new EntitySettingsService(entity)).reset();
        await (new BoWhiteListService(entity)).createOrUpdate([], false);
        await (new UserWhiteListService(entity)).createOrUpdate([], false);

        res.send(settings);
        next();
    } catch (err) {
        next(err);
    }
}

router.get("/entities/:path/settings", authenticate, authorize, decodePid(), getEntitySettings);
router.patch("/entities/:path/settings",
    authenticate,
    authorize,
    decodePid({ ignoredKeys: ["newDeferredPaymentPromoId"] }),
    auditable,
    patchSettings);
router.put("/entities/:path/settings",
    authenticate,
    authorize,
    decodePid({ ignoredKeys: ["newDeferredPaymentPromoId"] }),
    auditable,
    putSettings);
router.delete("/entities/:path/settings",
    authenticate,
    authorize,
    decodePid({ ignoredKeys: ["newDeferredPaymentPromoId"] }),
    auditable,
    resetEntitySettings);

router.get("/settings", authenticate, authorize, decodePid(), getEntitySettings);
router.patch("/settings",
    authenticate,
    authorize,
    decodePid({ ignoredKeys: ["newDeferredPaymentPromoId"] }),
    auditable,
    patchSettings);
router.put("/settings",
    authenticate,
    authorize,
    decodePid({ ignoredKeys: ["newDeferredPaymentPromoId"] }),
    auditable,
    putSettings);
router.delete("/settings",
    authenticate,
    authorize,
    decodePid({ ignoredKeys: ["newDeferredPaymentPromoId"] }),
    auditable,
    resetEntitySettings);
router.patch("/entities/:path/restricted-countries-solution",
    authenticate,
    authorize,
    decodePid({ ignoredKeys: ["newDeferredPaymentPromoId"] }),
    auditable,
    patchSettingsWithRestrictedCountriesSolution);

type IpWhiteListRequest = Request & KeyEntityHolder;

function getIpWhiteListService(req: IpWhiteListRequest): [string, IpWhiteListService] {
    const entity = getEntity(req, { ignoreSuspended: req.keyEntity.isMaster() });
    switch (req.params.type) {
        case "bo":
            return [BO_WHITELIST_KEY, new BoWhiteListService(entity)];
        case "user":
            return [USER_WHITELIST_KEY, new UserWhiteListService(entity)];
        default:
            throw new Errors.WhitelistNotFound();
    }
}

async function getEntityIpWhitelist(req: IpWhiteListRequest, res: Response, next: NextFunction) {
    try {
        const [, service] = getIpWhiteListService(req);
        res.send(await service.get());
        next();
    } catch (err) {
        next(err);
    }
}

async function patchEntityIpWhitelist(req: IpWhiteListRequest, res: Response, next: NextFunction) {
    try {
        const data = req.body;
        const error = validateIpList(data).shift();
        if (error) {
            return next(new Errors.ValidationError(error));
        }
        const [key, service] = getIpWhiteListService(req);
        const whitelist = await service.createOrUpdate(data);
        const settingsService = new EntitySettingsService(service.entity);
        await settingsService.patch({
            [key]: whitelist
        });
        res.send(whitelist);
        next();
    } catch (err) {
        next(err);
    }
}

async function deleteEntityIpWhitelist(req: IpWhiteListRequest, res: Response, next: NextFunction) {
    try {
        const data = req.body;
        const error = validateIpList(data).shift();
        if (error) {
            return next(new Errors.ValidationError(error));
        }
        const [key, service] = getIpWhiteListService(req);
        const whitelist = await service.remove(data);
        const settingsService = new EntitySettingsService(service.entity);
        await settingsService.patch({
            [key]: whitelist
        });
        res.send(whitelist);
        next();
    } catch (err) {
        next(err);
    }
}

router.route("/entities/:path/settings/bowhitelist")
    .all(authenticate, authorize, function(req: Request, res, next) {
        req.params.type = "bo";
        next();
    })
    .get(getEntityIpWhitelist)
    .post(auditable, patchEntityIpWhitelist)
    .patch(auditable, patchEntityIpWhitelist)
    .delete(auditable, deleteEntityIpWhitelist);

router.route("/entities/:path/settings/ip-whitelist/:type")
    .all(authenticate, authorize)
    .get(getEntityIpWhitelist)
    .post(auditable, patchEntityIpWhitelist)
    .patch(auditable, patchEntityIpWhitelist)
    .delete(auditable, deleteEntityIpWhitelist);

async function getEntityInfo(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const entity = getEntity(req, { ignoreSuspended: req.keyEntity.isMaster() });
        res.send(await EntityInfoService.getOne(entity, req.params.type) || {});
        next();
    } catch (err) {
        next(err);
    }
}

async function createEntityInfo(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const entity = getEntity(req);
        res.send(await EntityInfoService.create(entity, req.params.type, req.body));
        next();
    } catch (err) {
        next(err);
    }
}

async function createOrUpdateEntityInfo(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const entity = getEntity(req);
        const entityInfoRecord = await EntityInfoService.getOne(entity, req.params.type);
        if (entityInfoRecord) {
            res.send(await EntityInfoService.update(entity, req.params.type, req.body));
        } else {
            res.send(await EntityInfoService.create(entity, req.params.type, req.body));
        }
        next();
    } catch (err) {
        next(err);
    }
}

async function patchEntityInfo(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const entity = getEntity(req);
        res.send(await EntityInfoService.patch(entity, req.params.type, req.body));
        next();
    } catch (err) {
        next(err);
    }
}

async function deleteEntityInfo(req: Request & KeyEntityHolder, res: Response, next: NextFunction) {
    try {
        const entity = getEntity(req);
        await EntityInfoService.deleteInfoRecord(entity, req.params.type);
        res.status(204).end();
        next();
    } catch (err) {
        next(err);
    }
}

const validateType = validate({
    type: { notEmpty: true, isWord: true }
});

router.get("/entities/:path/info/:type", authenticate, authorize, validateType, getEntityInfo);
router.post("/entities/:path/info/:type", authenticate, authorize, validateType, auditable, createEntityInfo);
router.put("/entities/:path/info/:type", authenticate, authorize, validateType, auditable, createOrUpdateEntityInfo);
router.patch("/entities/:path/info/:type", authenticate, authorize, validateType, auditable, patchEntityInfo);
router.delete("/entities/:path/info/:type", authenticate, authorize, validateType, auditable, deleteEntityInfo);

router.get("/info/:type", authenticate, authorize, validateType, getEntityInfo);
router.post("/info/:type", authenticate, authorize, validateType, auditable, createEntityInfo);
router.put("/info/:type", authenticate, authorize, validateType, auditable, createOrUpdateEntityInfo);
router.patch("/info/:type", authenticate, authorize, validateType, auditable, patchEntityInfo);
router.delete("/info/:type", authenticate, authorize, validateType, auditable, deleteEntityInfo);

export default router;
