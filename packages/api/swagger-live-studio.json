{"swagger": "2.0", "info": {"description": "Skywind - API for Live Studio", "version": "5.58", "title": "Skywind - API for Live Studio"}, "basePath": "/v1", "produces": ["application/json"], "securityDefinitions": {"apiKey": {"description": "Tokens contains live studio information", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-live-studio-token", "in": "header"}}, "paths": {"/brands/{brandId}/players/{playerCode}": {"get": {"tags": ["Players"], "security": [{"apiKey": []}], "summary": "Gets player", "parameters": [{"name": "brandId", "in": "path", "description": "Brand identifier", "required": true, "type": "string"}, {"name": "playerCode", "in": "path", "description": "Player code", "required": true, "type": "string"}], "responses": {"200": {"description": "Player information", "schema": {"$ref": "#/definitions/PlayerInfo"}}, "400": {"description": "- 40: Validation error"}}}, "put": {"tags": ["Players"], "security": [{"apiKey": []}], "summary": "Updates player", "parameters": [{"name": "brandId", "in": "path", "description": "Brand identifier", "required": true, "type": "string"}, {"name": "playerCode", "in": "path", "description": "Player code", "required": true, "type": "string"}, {"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/PlayerInfo"}}], "responses": {"200": {"description": "Player information", "schema": {"$ref": "#/definitions/PlayerInfo"}}, "400": {"description": "- 40: Validation error"}}}}, "/players/search": {"post": {"tags": ["Players"], "security": [{"apiKey": []}], "summary": "Gets players", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/SearchPlayer"}}], "responses": {"200": {"description": "Players information", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerInfo"}}}, "400": {"description": "- 40: Validation error"}}}}, "/token": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Generates token", "responses": {"200": {"description": "Live studio token", "schema": {"type": "array", "items": {"$ref": "#/definitions/LiveStudioTokenInfo"}}}}}}}, "parameters": {"brandId": {"name": "brandId", "in": "query", "description": "Brand ID", "type": "number", "required": true}, "offset": {"name": "offset", "in": "query", "description": "Result list offset", "required": false, "type": "integer", "default": 0}, "limit": {"name": "limit", "in": "query", "description": "Result list limit", "required": false, "type": "integer", "default": 20}, "sortBy": {"name": "sortBy", "in": "query", "description": "Sorting key", "required": false, "type": "string"}, "sortOrder": {"name": "sortOrder", "in": "query", "description": "Sorting order", "required": false, "type": "string", "enum": ["ASC", "DESC"]}, "ts": {"name": "ts", "in": "query", "description": "Timestamp (ISO 8601 timestamp)", "required": false, "type": "string"}, "ts__lt": {"name": "ts__lt", "in": "query", "description": "Timestamp (ISO 8601 timestamp)", "required": false, "type": "string"}, "ts__lte": {"name": "ts__lte", "in": "query", "description": "Timestamp (ISO 8601 timestamp)", "required": false, "type": "string"}}, "definitions": {"Error": {"type": "object", "properties": {"code": {"type": "integer", "description": "error code", "example": 677}, "message": {"type": "string", "description": "error message", "example": "Negative transaction operation value"}}}, "SearchPlayer": {"type": "object", "properties": {"where": {"type": "object"}, "orderBy": {"type": "object", "properties": {"playerCode": {"type": "string", "example": "ASC"}}}, "offset": {"type": "integer", "example": 3}, "limit": {"type": "integer", "example": 3}}}, "PlayerInfo": {"type": "object", "properties": {"playerCode": {"type": "string", "example": "ev7tsaT20200922T120516"}, "nickname": {"type": "string", "example": "ev7tsaT20200922T120516"}, "brandId": {"type": "integer", "example": 3}, "isVip": {"type": "boolean", "example": true}, "isTracked": {"type": "boolean", "example": true}, "isPublicChatBlock": {"type": "boolean", "example": false}, "isPrivateChatBlock": {"type": "boolean", "example": false}, "isMerchantPlayer": {"type": "boolean", "example": false}, "hasWarn": {"type": "boolean", "example": false}}}, "LiveStudioTokenInfo": {"type": "object", "properties": {"liveStudioToken": {"type": "string", "example": "token"}}}}}